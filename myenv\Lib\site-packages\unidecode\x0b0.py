data = (
'ggwem',    # 0x00
'ggweb',    # 0x01
'ggwebs',    # 0x02
'ggwes',    # 0x03
'ggwess',    # 0x04
'ggweng',    # 0x05
'ggwej',    # 0x06
'ggwec',    # 0x07
'ggwek',    # 0x08
'ggwet',    # 0x09
'ggwep',    # 0x0a
'ggweh',    # 0x0b
'ggwi',    # 0x0c
'ggwig',    # 0x0d
'ggwigg',    # 0x0e
'ggwigs',    # 0x0f
'ggwin',    # 0x10
'ggwinj',    # 0x11
'ggwinh',    # 0x12
'ggwid',    # 0x13
'ggwil',    # 0x14
'ggwilg',    # 0x15
'ggwilm',    # 0x16
'ggwilb',    # 0x17
'ggwils',    # 0x18
'ggwilt',    # 0x19
'ggwilp',    # 0x1a
'ggwilh',    # 0x1b
'ggwim',    # 0x1c
'ggwib',    # 0x1d
'ggwibs',    # 0x1e
'ggwis',    # 0x1f
'ggwiss',    # 0x20
'ggwing',    # 0x21
'ggwij',    # 0x22
'ggwic',    # 0x23
'ggwik',    # 0x24
'ggwit',    # 0x25
'ggwip',    # 0x26
'ggwih',    # 0x27
'ggyu',    # 0x28
'ggyug',    # 0x29
'ggyugg',    # 0x2a
'ggyugs',    # 0x2b
'ggyun',    # 0x2c
'ggyunj',    # 0x2d
'ggyunh',    # 0x2e
'ggyud',    # 0x2f
'ggyul',    # 0x30
'ggyulg',    # 0x31
'ggyulm',    # 0x32
'ggyulb',    # 0x33
'ggyuls',    # 0x34
'ggyult',    # 0x35
'ggyulp',    # 0x36
'ggyulh',    # 0x37
'ggyum',    # 0x38
'ggyub',    # 0x39
'ggyubs',    # 0x3a
'ggyus',    # 0x3b
'ggyuss',    # 0x3c
'ggyung',    # 0x3d
'ggyuj',    # 0x3e
'ggyuc',    # 0x3f
'ggyuk',    # 0x40
'ggyut',    # 0x41
'ggyup',    # 0x42
'ggyuh',    # 0x43
'ggeu',    # 0x44
'ggeug',    # 0x45
'ggeugg',    # 0x46
'ggeugs',    # 0x47
'ggeun',    # 0x48
'ggeunj',    # 0x49
'ggeunh',    # 0x4a
'ggeud',    # 0x4b
'ggeul',    # 0x4c
'ggeulg',    # 0x4d
'ggeulm',    # 0x4e
'ggeulb',    # 0x4f
'ggeuls',    # 0x50
'ggeult',    # 0x51
'ggeulp',    # 0x52
'ggeulh',    # 0x53
'ggeum',    # 0x54
'ggeub',    # 0x55
'ggeubs',    # 0x56
'ggeus',    # 0x57
'ggeuss',    # 0x58
'ggeung',    # 0x59
'ggeuj',    # 0x5a
'ggeuc',    # 0x5b
'ggeuk',    # 0x5c
'ggeut',    # 0x5d
'ggeup',    # 0x5e
'ggeuh',    # 0x5f
'ggyi',    # 0x60
'ggyig',    # 0x61
'ggyigg',    # 0x62
'ggyigs',    # 0x63
'ggyin',    # 0x64
'ggyinj',    # 0x65
'ggyinh',    # 0x66
'ggyid',    # 0x67
'ggyil',    # 0x68
'ggyilg',    # 0x69
'ggyilm',    # 0x6a
'ggyilb',    # 0x6b
'ggyils',    # 0x6c
'ggyilt',    # 0x6d
'ggyilp',    # 0x6e
'ggyilh',    # 0x6f
'ggyim',    # 0x70
'ggyib',    # 0x71
'ggyibs',    # 0x72
'ggyis',    # 0x73
'ggyiss',    # 0x74
'ggying',    # 0x75
'ggyij',    # 0x76
'ggyic',    # 0x77
'ggyik',    # 0x78
'ggyit',    # 0x79
'ggyip',    # 0x7a
'ggyih',    # 0x7b
'ggi',    # 0x7c
'ggig',    # 0x7d
'ggigg',    # 0x7e
'ggigs',    # 0x7f
'ggin',    # 0x80
'gginj',    # 0x81
'gginh',    # 0x82
'ggid',    # 0x83
'ggil',    # 0x84
'ggilg',    # 0x85
'ggilm',    # 0x86
'ggilb',    # 0x87
'ggils',    # 0x88
'ggilt',    # 0x89
'ggilp',    # 0x8a
'ggilh',    # 0x8b
'ggim',    # 0x8c
'ggib',    # 0x8d
'ggibs',    # 0x8e
'ggis',    # 0x8f
'ggiss',    # 0x90
'gging',    # 0x91
'ggij',    # 0x92
'ggic',    # 0x93
'ggik',    # 0x94
'ggit',    # 0x95
'ggip',    # 0x96
'ggih',    # 0x97
'na',    # 0x98
'nag',    # 0x99
'nagg',    # 0x9a
'nags',    # 0x9b
'nan',    # 0x9c
'nanj',    # 0x9d
'nanh',    # 0x9e
'nad',    # 0x9f
'nal',    # 0xa0
'nalg',    # 0xa1
'nalm',    # 0xa2
'nalb',    # 0xa3
'nals',    # 0xa4
'nalt',    # 0xa5
'nalp',    # 0xa6
'nalh',    # 0xa7
'nam',    # 0xa8
'nab',    # 0xa9
'nabs',    # 0xaa
'nas',    # 0xab
'nass',    # 0xac
'nang',    # 0xad
'naj',    # 0xae
'nac',    # 0xaf
'nak',    # 0xb0
'nat',    # 0xb1
'nap',    # 0xb2
'nah',    # 0xb3
'nae',    # 0xb4
'naeg',    # 0xb5
'naegg',    # 0xb6
'naegs',    # 0xb7
'naen',    # 0xb8
'naenj',    # 0xb9
'naenh',    # 0xba
'naed',    # 0xbb
'nael',    # 0xbc
'naelg',    # 0xbd
'naelm',    # 0xbe
'naelb',    # 0xbf
'naels',    # 0xc0
'naelt',    # 0xc1
'naelp',    # 0xc2
'naelh',    # 0xc3
'naem',    # 0xc4
'naeb',    # 0xc5
'naebs',    # 0xc6
'naes',    # 0xc7
'naess',    # 0xc8
'naeng',    # 0xc9
'naej',    # 0xca
'naec',    # 0xcb
'naek',    # 0xcc
'naet',    # 0xcd
'naep',    # 0xce
'naeh',    # 0xcf
'nya',    # 0xd0
'nyag',    # 0xd1
'nyagg',    # 0xd2
'nyags',    # 0xd3
'nyan',    # 0xd4
'nyanj',    # 0xd5
'nyanh',    # 0xd6
'nyad',    # 0xd7
'nyal',    # 0xd8
'nyalg',    # 0xd9
'nyalm',    # 0xda
'nyalb',    # 0xdb
'nyals',    # 0xdc
'nyalt',    # 0xdd
'nyalp',    # 0xde
'nyalh',    # 0xdf
'nyam',    # 0xe0
'nyab',    # 0xe1
'nyabs',    # 0xe2
'nyas',    # 0xe3
'nyass',    # 0xe4
'nyang',    # 0xe5
'nyaj',    # 0xe6
'nyac',    # 0xe7
'nyak',    # 0xe8
'nyat',    # 0xe9
'nyap',    # 0xea
'nyah',    # 0xeb
'nyae',    # 0xec
'nyaeg',    # 0xed
'nyaegg',    # 0xee
'nyaegs',    # 0xef
'nyaen',    # 0xf0
'nyaenj',    # 0xf1
'nyaenh',    # 0xf2
'nyaed',    # 0xf3
'nyael',    # 0xf4
'nyaelg',    # 0xf5
'nyaelm',    # 0xf6
'nyaelb',    # 0xf7
'nyaels',    # 0xf8
'nyaelt',    # 0xf9
'nyaelp',    # 0xfa
'nyaelh',    # 0xfb
'nyaem',    # 0xfc
'nyaeb',    # 0xfd
'nyaebs',    # 0xfe
'nyaes',    # 0xff
)
