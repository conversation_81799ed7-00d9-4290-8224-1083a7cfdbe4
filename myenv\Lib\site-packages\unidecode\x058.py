data = (
'Ku ',    # 0x00
'Ke ',    # 0x01
'Tang ',    # 0x02
'Kun ',    # 0x03
'Ni ',    # 0x04
'Jian ',    # 0x05
'Dui ',    # 0x06
'<PERSON> ',    # 0x07
'<PERSON> ',    # 0x08
'Yu ',    # 0x09
'E ',    # 0x0a
'Peng ',    # 0x0b
'Gu ',    # 0x0c
'Tu ',    # 0x0d
'Leng ',    # 0x0e
None,    # 0x0f
'Ya ',    # 0x10
'Qian ',    # 0x11
None,    # 0x12
'An ',    # 0x13
None,    # 0x14
'Duo ',    # 0x15
'Nao ',    # 0x16
'Tu ',    # 0x17
'Cheng ',    # 0x18
'Yin ',    # 0x19
'Hun ',    # 0x1a
'Bi ',    # 0x1b
'Lian ',    # 0x1c
'Guo ',    # 0x1d
'Die ',    # 0x1e
'Zhuan ',    # 0x1f
'Hou ',    # 0x20
'Bao ',    # 0x21
'Bao ',    # 0x22
'Yu ',    # 0x23
'Di ',    # 0x24
'Mao ',    # 0x25
'Jie ',    # 0x26
'Ruan ',    # 0x27
'E ',    # 0x28
'Geng ',    # 0x29
'Kan ',    # 0x2a
'Zong ',    # 0x2b
'Yu ',    # 0x2c
'Huang ',    # 0x2d
'E ',    # 0x2e
'Yao ',    # 0x2f
'Yan ',    # 0x30
'Bao ',    # 0x31
'Ji ',    # 0x32
'Mei ',    # 0x33
'Chang ',    # 0x34
'Du ',    # 0x35
'Tuo ',    # 0x36
'Yin ',    # 0x37
'Feng ',    # 0x38
'Zhong ',    # 0x39
'Jie ',    # 0x3a
'Zhen ',    # 0x3b
'Feng ',    # 0x3c
'Gang ',    # 0x3d
'Chuan ',    # 0x3e
'Jian ',    # 0x3f
'Pyeng ',    # 0x40
'Toride ',    # 0x41
'Xiang ',    # 0x42
'Huang ',    # 0x43
'Leng ',    # 0x44
'Duan ',    # 0x45
None,    # 0x46
'Xuan ',    # 0x47
'Ji ',    # 0x48
'Ji ',    # 0x49
'Kuai ',    # 0x4a
'Ying ',    # 0x4b
'Ta ',    # 0x4c
'Cheng ',    # 0x4d
'Yong ',    # 0x4e
'Kai ',    # 0x4f
'Su ',    # 0x50
'Su ',    # 0x51
'Shi ',    # 0x52
'Mi ',    # 0x53
'Ta ',    # 0x54
'Weng ',    # 0x55
'Cheng ',    # 0x56
'Tu ',    # 0x57
'Tang ',    # 0x58
'Que ',    # 0x59
'Zhong ',    # 0x5a
'Li ',    # 0x5b
'Peng ',    # 0x5c
'Bang ',    # 0x5d
'Sai ',    # 0x5e
'Zang ',    # 0x5f
'Dui ',    # 0x60
'Tian ',    # 0x61
'Wu ',    # 0x62
'Cheng ',    # 0x63
'Xun ',    # 0x64
'Ge ',    # 0x65
'Zhen ',    # 0x66
'Ai ',    # 0x67
'Gong ',    # 0x68
'Yan ',    # 0x69
'Kan ',    # 0x6a
'Tian ',    # 0x6b
'Yuan ',    # 0x6c
'Wen ',    # 0x6d
'Xie ',    # 0x6e
'Liu ',    # 0x6f
'Ama ',    # 0x70
'Lang ',    # 0x71
'Chang ',    # 0x72
'Peng ',    # 0x73
'Beng ',    # 0x74
'Chen ',    # 0x75
'Cu ',    # 0x76
'Lu ',    # 0x77
'Ou ',    # 0x78
'Qian ',    # 0x79
'Mei ',    # 0x7a
'Mo ',    # 0x7b
'Zhuan ',    # 0x7c
'Shuang ',    # 0x7d
'Shu ',    # 0x7e
'Lou ',    # 0x7f
'Chi ',    # 0x80
'Man ',    # 0x81
'Biao ',    # 0x82
'Jing ',    # 0x83
'Qi ',    # 0x84
'Shu ',    # 0x85
'Di ',    # 0x86
'Zhang ',    # 0x87
'Kan ',    # 0x88
'Yong ',    # 0x89
'Dian ',    # 0x8a
'Chen ',    # 0x8b
'Zhi ',    # 0x8c
'Xi ',    # 0x8d
'Guo ',    # 0x8e
'Qiang ',    # 0x8f
'Jin ',    # 0x90
'Di ',    # 0x91
'Shang ',    # 0x92
'Mu ',    # 0x93
'Cui ',    # 0x94
'Yan ',    # 0x95
'Ta ',    # 0x96
'Zeng ',    # 0x97
'Qi ',    # 0x98
'Qiang ',    # 0x99
'Liang ',    # 0x9a
None,    # 0x9b
'Zhui ',    # 0x9c
'Qiao ',    # 0x9d
'Zeng ',    # 0x9e
'Xu ',    # 0x9f
'Shan ',    # 0xa0
'Shan ',    # 0xa1
'Ba ',    # 0xa2
'Pu ',    # 0xa3
'Kuai ',    # 0xa4
'Dong ',    # 0xa5
'Fan ',    # 0xa6
'Que ',    # 0xa7
'Mo ',    # 0xa8
'Dun ',    # 0xa9
'Dun ',    # 0xaa
'Dun ',    # 0xab
'Di ',    # 0xac
'Sheng ',    # 0xad
'Duo ',    # 0xae
'Duo ',    # 0xaf
'Tan ',    # 0xb0
'Deng ',    # 0xb1
'Wu ',    # 0xb2
'Fen ',    # 0xb3
'Huang ',    # 0xb4
'Tan ',    # 0xb5
'Da ',    # 0xb6
'Ye ',    # 0xb7
'Sho ',    # 0xb8
'Mama ',    # 0xb9
'Yu ',    # 0xba
'Qiang ',    # 0xbb
'Ji ',    # 0xbc
'Qiao ',    # 0xbd
'Ken ',    # 0xbe
'Yi ',    # 0xbf
'Pi ',    # 0xc0
'Bi ',    # 0xc1
'Dian ',    # 0xc2
'Jiang ',    # 0xc3
'Ye ',    # 0xc4
'Yong ',    # 0xc5
'Bo ',    # 0xc6
'Tan ',    # 0xc7
'Lan ',    # 0xc8
'Ju ',    # 0xc9
'Huai ',    # 0xca
'Dang ',    # 0xcb
'Rang ',    # 0xcc
'Qian ',    # 0xcd
'Xun ',    # 0xce
'Lan ',    # 0xcf
'Xi ',    # 0xd0
'He ',    # 0xd1
'Ai ',    # 0xd2
'Ya ',    # 0xd3
'Dao ',    # 0xd4
'Hao ',    # 0xd5
'Ruan ',    # 0xd6
'Mama ',    # 0xd7
'Lei ',    # 0xd8
'Kuang ',    # 0xd9
'Lu ',    # 0xda
'Yan ',    # 0xdb
'Tan ',    # 0xdc
'Wei ',    # 0xdd
'Huai ',    # 0xde
'Long ',    # 0xdf
'Long ',    # 0xe0
'Rui ',    # 0xe1
'Li ',    # 0xe2
'Lin ',    # 0xe3
'Rang ',    # 0xe4
'Ten ',    # 0xe5
'Xun ',    # 0xe6
'Yan ',    # 0xe7
'Lei ',    # 0xe8
'Ba ',    # 0xe9
None,    # 0xea
'Shi ',    # 0xeb
'Ren ',    # 0xec
None,    # 0xed
'Zhuang ',    # 0xee
'Zhuang ',    # 0xef
'Sheng ',    # 0xf0
'Yi ',    # 0xf1
'Mai ',    # 0xf2
'Ke ',    # 0xf3
'Zhu ',    # 0xf4
'Zhuang ',    # 0xf5
'Hu ',    # 0xf6
'Hu ',    # 0xf7
'Kun ',    # 0xf8
'Yi ',    # 0xf9
'Hu ',    # 0xfa
'Xu ',    # 0xfb
'Kun ',    # 0xfc
'Shou ',    # 0xfd
'Mang ',    # 0xfe
'Zun ',    # 0xff
)
