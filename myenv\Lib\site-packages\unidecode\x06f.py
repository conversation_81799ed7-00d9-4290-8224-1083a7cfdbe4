data = (
'Qing ',    # 0x00
'Yu ',    # 0x01
'<PERSON><PERSON> ',    # 0x02
'Ji ',    # 0x03
'Ya ',    # 0x04
'<PERSON><PERSON> ',    # 0x05
'Qi ',    # 0x06
'Xi ',    # 0x07
'Ji ',    # 0x08
'Lu ',    # 0x09
'Lu ',    # 0x0a
'Long ',    # 0x0b
'<PERSON> ',    # 0x0c
'<PERSON> ',    # 0x0d
'Cong ',    # 0x0e
'<PERSON> ',    # 0x0f
'Zhi ',    # 0x10
'Gai ',    # 0x11
'Qiang ',    # 0x12
'Li ',    # 0x13
'Yan ',    # 0x14
'Cao ',    # 0x15
'Jiao ',    # 0x16
'Cong ',    # 0x17
'Qun ',    # 0x18
'Tuan ',    # 0x19
'Ou ',    # 0x1a
'Teng ',    # 0x1b
'Ye ',    # 0x1c
'Xi ',    # 0x1d
'Mi ',    # 0x1e
'Tang ',    # 0x1f
'Mo ',    # 0x20
'Shang ',    # 0x21
'Han ',    # 0x22
'Lian ',    # 0x23
'Lan ',    # 0x24
'Wa ',    # 0x25
'Li ',    # 0x26
'Qian ',    # 0x27
'<PERSON> ',    # 0x28
'Xuan ',    # 0x29
'Yi ',    # 0x2a
'Man ',    # 0x2b
'Zi ',    # 0x2c
'Mang ',    # 0x2d
'Kang ',    # 0x2e
'Lei ',    # 0x2f
'Peng ',    # 0x30
'Shu ',    # 0x31
'Zhang ',    # 0x32
'Zhang ',    # 0x33
'Chong ',    # 0x34
'Xu ',    # 0x35
'Huan ',    # 0x36
'Kuo ',    # 0x37
'Jian ',    # 0x38
'Yan ',    # 0x39
'Chuang ',    # 0x3a
'Liao ',    # 0x3b
'Cui ',    # 0x3c
'Ti ',    # 0x3d
'Yang ',    # 0x3e
'Jiang ',    # 0x3f
'Cong ',    # 0x40
'Ying ',    # 0x41
'Hong ',    # 0x42
'Xun ',    # 0x43
'Shu ',    # 0x44
'Guan ',    # 0x45
'Ying ',    # 0x46
'Xiao ',    # 0x47
None,    # 0x48
None,    # 0x49
'Xu ',    # 0x4a
'Lian ',    # 0x4b
'Zhi ',    # 0x4c
'Wei ',    # 0x4d
'Pi ',    # 0x4e
'Jue ',    # 0x4f
'Jiao ',    # 0x50
'Po ',    # 0x51
'Dang ',    # 0x52
'Hui ',    # 0x53
'Jie ',    # 0x54
'Wu ',    # 0x55
'Pa ',    # 0x56
'Ji ',    # 0x57
'Pan ',    # 0x58
'Gui ',    # 0x59
'Xiao ',    # 0x5a
'Qian ',    # 0x5b
'Qian ',    # 0x5c
'Xi ',    # 0x5d
'Lu ',    # 0x5e
'Xi ',    # 0x5f
'Xuan ',    # 0x60
'Dun ',    # 0x61
'Huang ',    # 0x62
'Min ',    # 0x63
'Run ',    # 0x64
'Su ',    # 0x65
'Liao ',    # 0x66
'Zhen ',    # 0x67
'Zhong ',    # 0x68
'Yi ',    # 0x69
'Di ',    # 0x6a
'Wan ',    # 0x6b
'Dan ',    # 0x6c
'Tan ',    # 0x6d
'Chao ',    # 0x6e
'Xun ',    # 0x6f
'Kui ',    # 0x70
'Yie ',    # 0x71
'Shao ',    # 0x72
'Tu ',    # 0x73
'Zhu ',    # 0x74
'San ',    # 0x75
'Hei ',    # 0x76
'Bi ',    # 0x77
'Shan ',    # 0x78
'Chan ',    # 0x79
'Chan ',    # 0x7a
'Shu ',    # 0x7b
'Tong ',    # 0x7c
'Pu ',    # 0x7d
'Lin ',    # 0x7e
'Wei ',    # 0x7f
'Se ',    # 0x80
'Se ',    # 0x81
'Cheng ',    # 0x82
'Jiong ',    # 0x83
'Cheng ',    # 0x84
'Hua ',    # 0x85
'Jiao ',    # 0x86
'Lao ',    # 0x87
'Che ',    # 0x88
'Gan ',    # 0x89
'Cun ',    # 0x8a
'Heng ',    # 0x8b
'Si ',    # 0x8c
'Shu ',    # 0x8d
'Peng ',    # 0x8e
'Han ',    # 0x8f
'Yun ',    # 0x90
'Liu ',    # 0x91
'Hong ',    # 0x92
'Fu ',    # 0x93
'Hao ',    # 0x94
'He ',    # 0x95
'Xian ',    # 0x96
'Jian ',    # 0x97
'Shan ',    # 0x98
'Xi ',    # 0x99
'Oki ',    # 0x9a
None,    # 0x9b
'Lan ',    # 0x9c
None,    # 0x9d
'Yu ',    # 0x9e
'Lin ',    # 0x9f
'Min ',    # 0xa0
'Zao ',    # 0xa1
'Dang ',    # 0xa2
'Wan ',    # 0xa3
'Ze ',    # 0xa4
'Xie ',    # 0xa5
'Yu ',    # 0xa6
'Li ',    # 0xa7
'Shi ',    # 0xa8
'Xue ',    # 0xa9
'Ling ',    # 0xaa
'Man ',    # 0xab
'Zi ',    # 0xac
'Yong ',    # 0xad
'Kuai ',    # 0xae
'Can ',    # 0xaf
'Lian ',    # 0xb0
'Dian ',    # 0xb1
'Ye ',    # 0xb2
'Ao ',    # 0xb3
'Huan ',    # 0xb4
'Zhen ',    # 0xb5
'Chan ',    # 0xb6
'Man ',    # 0xb7
'Dan ',    # 0xb8
'Dan ',    # 0xb9
'Yi ',    # 0xba
'Sui ',    # 0xbb
'Pi ',    # 0xbc
'Ju ',    # 0xbd
'Ta ',    # 0xbe
'Qin ',    # 0xbf
'Ji ',    # 0xc0
'Zhuo ',    # 0xc1
'Lian ',    # 0xc2
'Nong ',    # 0xc3
'Guo ',    # 0xc4
'Jin ',    # 0xc5
'Fen ',    # 0xc6
'Se ',    # 0xc7
'Ji ',    # 0xc8
'Sui ',    # 0xc9
'Hui ',    # 0xca
'Chu ',    # 0xcb
'Ta ',    # 0xcc
'Song ',    # 0xcd
'Ding ',    # 0xce
None,    # 0xcf
'Zhu ',    # 0xd0
'Lai ',    # 0xd1
'Bin ',    # 0xd2
'Lian ',    # 0xd3
'Mi ',    # 0xd4
'Shi ',    # 0xd5
'Shu ',    # 0xd6
'Mi ',    # 0xd7
'Ning ',    # 0xd8
'Ying ',    # 0xd9
'Ying ',    # 0xda
'Meng ',    # 0xdb
'Jin ',    # 0xdc
'Qi ',    # 0xdd
'Pi ',    # 0xde
'Ji ',    # 0xdf
'Hao ',    # 0xe0
'Ru ',    # 0xe1
'Zui ',    # 0xe2
'Wo ',    # 0xe3
'Tao ',    # 0xe4
'Yin ',    # 0xe5
'Yin ',    # 0xe6
'Dui ',    # 0xe7
'Ci ',    # 0xe8
'Huo ',    # 0xe9
'Jing ',    # 0xea
'Lan ',    # 0xeb
'Jun ',    # 0xec
'Ai ',    # 0xed
'Pu ',    # 0xee
'Zhuo ',    # 0xef
'Wei ',    # 0xf0
'Bin ',    # 0xf1
'Gu ',    # 0xf2
'Qian ',    # 0xf3
'Xing ',    # 0xf4
'Hama ',    # 0xf5
'Kuo ',    # 0xf6
'Fei ',    # 0xf7
None,    # 0xf8
'Boku ',    # 0xf9
'Jian ',    # 0xfa
'Wei ',    # 0xfb
'Luo ',    # 0xfc
'Zan ',    # 0xfd
'Lu ',    # 0xfe
'Li ',    # 0xff
)
