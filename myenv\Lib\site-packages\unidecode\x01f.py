data = (
'a',    # 0x00
'a',    # 0x01
'a',    # 0x02
'a',    # 0x03
'a',    # 0x04
'a',    # 0x05
'a',    # 0x06
'a',    # 0x07
'A',    # 0x08
'A',    # 0x09
'A',    # 0x0a
'A',    # 0x0b
'A',    # 0x0c
'A',    # 0x0d
'A',    # 0x0e
'A',    # 0x0f
'e',    # 0x10
'e',    # 0x11
'e',    # 0x12
'e',    # 0x13
'e',    # 0x14
'e',    # 0x15
None,    # 0x16
None,    # 0x17
'E',    # 0x18
'E',    # 0x19
'E',    # 0x1a
'E',    # 0x1b
'E',    # 0x1c
'E',    # 0x1d
None,    # 0x1e
None,    # 0x1f
'e',    # 0x20
'e',    # 0x21
'e',    # 0x22
'e',    # 0x23
'e',    # 0x24
'e',    # 0x25
'e',    # 0x26
'e',    # 0x27
'E',    # 0x28
'E',    # 0x29
'E',    # 0x2a
'E',    # 0x2b
'E',    # 0x2c
'E',    # 0x2d
'E',    # 0x2e
'E',    # 0x2f
'i',    # 0x30
'i',    # 0x31
'i',    # 0x32
'i',    # 0x33
'i',    # 0x34
'i',    # 0x35
'i',    # 0x36
'i',    # 0x37
'I',    # 0x38
'I',    # 0x39
'I',    # 0x3a
'I',    # 0x3b
'I',    # 0x3c
'I',    # 0x3d
'I',    # 0x3e
'I',    # 0x3f
'o',    # 0x40
'o',    # 0x41
'o',    # 0x42
'o',    # 0x43
'o',    # 0x44
'o',    # 0x45
None,    # 0x46
None,    # 0x47
'O',    # 0x48
'O',    # 0x49
'O',    # 0x4a
'O',    # 0x4b
'O',    # 0x4c
'O',    # 0x4d
None,    # 0x4e
None,    # 0x4f
'u',    # 0x50
'u',    # 0x51
'u',    # 0x52
'u',    # 0x53
'u',    # 0x54
'u',    # 0x55
'u',    # 0x56
'u',    # 0x57
None,    # 0x58
'U',    # 0x59
None,    # 0x5a
'U',    # 0x5b
None,    # 0x5c
'U',    # 0x5d
None,    # 0x5e
'U',    # 0x5f
'o',    # 0x60
'o',    # 0x61
'o',    # 0x62
'o',    # 0x63
'o',    # 0x64
'o',    # 0x65
'o',    # 0x66
'o',    # 0x67
'O',    # 0x68
'O',    # 0x69
'O',    # 0x6a
'O',    # 0x6b
'O',    # 0x6c
'O',    # 0x6d
'O',    # 0x6e
'O',    # 0x6f
'a',    # 0x70
'a',    # 0x71
'e',    # 0x72
'e',    # 0x73
'e',    # 0x74
'e',    # 0x75
'i',    # 0x76
'i',    # 0x77
'o',    # 0x78
'o',    # 0x79
'u',    # 0x7a
'u',    # 0x7b
'o',    # 0x7c
'o',    # 0x7d
None,    # 0x7e
None,    # 0x7f
'a',    # 0x80
'a',    # 0x81
'a',    # 0x82
'a',    # 0x83
'a',    # 0x84
'a',    # 0x85
'a',    # 0x86
'a',    # 0x87
'A',    # 0x88
'A',    # 0x89
'A',    # 0x8a
'A',    # 0x8b
'A',    # 0x8c
'A',    # 0x8d
'A',    # 0x8e
'A',    # 0x8f
'e',    # 0x90
'e',    # 0x91
'e',    # 0x92
'e',    # 0x93
'e',    # 0x94
'e',    # 0x95
'e',    # 0x96
'e',    # 0x97
'E',    # 0x98
'E',    # 0x99
'E',    # 0x9a
'E',    # 0x9b
'E',    # 0x9c
'E',    # 0x9d
'E',    # 0x9e
'E',    # 0x9f
'o',    # 0xa0
'o',    # 0xa1
'o',    # 0xa2
'o',    # 0xa3
'o',    # 0xa4
'o',    # 0xa5
'o',    # 0xa6
'o',    # 0xa7
'O',    # 0xa8
'O',    # 0xa9
'O',    # 0xaa
'O',    # 0xab
'O',    # 0xac
'O',    # 0xad
'O',    # 0xae
'O',    # 0xaf
'a',    # 0xb0
'a',    # 0xb1
'a',    # 0xb2
'a',    # 0xb3
'a',    # 0xb4
None,    # 0xb5
'a',    # 0xb6
'a',    # 0xb7
'A',    # 0xb8
'A',    # 0xb9
'A',    # 0xba
'A',    # 0xbb
'A',    # 0xbc
'\'',    # 0xbd
'i',    # 0xbe
'\'',    # 0xbf
'~',    # 0xc0
'"~',    # 0xc1
'e',    # 0xc2
'e',    # 0xc3
'e',    # 0xc4
None,    # 0xc5
'e',    # 0xc6
'e',    # 0xc7
'E',    # 0xc8
'E',    # 0xc9
'E',    # 0xca
'E',    # 0xcb
'E',    # 0xcc
'\'`',    # 0xcd
'\'\'',    # 0xce
'\'~',    # 0xcf
'i',    # 0xd0
'i',    # 0xd1
'i',    # 0xd2
'i',    # 0xd3
None,    # 0xd4
None,    # 0xd5
'i',    # 0xd6
'i',    # 0xd7
'I',    # 0xd8
'I',    # 0xd9
'I',    # 0xda
'I',    # 0xdb
None,    # 0xdc
'`\'',    # 0xdd
'`\'',    # 0xde
'`~',    # 0xdf
'u',    # 0xe0
'u',    # 0xe1
'u',    # 0xe2
'u',    # 0xe3
'R',    # 0xe4
'R',    # 0xe5
'u',    # 0xe6
'u',    # 0xe7
'U',    # 0xe8
'U',    # 0xe9
'U',    # 0xea
'U',    # 0xeb
'R',    # 0xec
'"`',    # 0xed
'"\'',    # 0xee
'`',    # 0xef
None,    # 0xf0
None,    # 0xf1
'o',    # 0xf2
'o',    # 0xf3
'o',    # 0xf4
None,    # 0xf5
'o',    # 0xf6
'o',    # 0xf7
'O',    # 0xf8
'O',    # 0xf9
'O',    # 0xfa
'O',    # 0xfb
'O',    # 0xfc
'\'',    # 0xfd
'`',    # 0xfe
)
