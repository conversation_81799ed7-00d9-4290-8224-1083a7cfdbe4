data = (
'<PERSON> ',    # 0x00
'Tiao ',    # 0x01
'Zhi ',    # 0x02
'<PERSON>ui ',    # 0x03
'<PERSON> ',    # 0x04
'<PERSON>e ',    # 0x05
'<PERSON>ui ',    # 0x06
'<PERSON>e ',    # 0x07
'<PERSON> ',    # 0x08
'<PERSON> ',    # 0x09
'Ji ',    # 0x0a
'<PERSON><PERSON><PERSON><PERSON><PERSON> ',    # 0x0b
None,    # 0x0c
'<PERSON>ai ',    # 0x0d
'Sa ',    # 0x0e
'Zang ',    # 0x0f
'Qi ',    # 0x10
'Nao ',    # 0x11
'Mi ',    # 0x12
'Nong ',    # 0x13
'Luan ',    # 0x14
'Wan ',    # 0x15
'Bo ',    # 0x16
'Wen ',    # 0x17
'Guan ',    # 0x18
'Qiu ',    # 0x19
'Jiao ',    # 0x1a
'Jing ',    # 0x1b
'Rou ',    # 0x1c
'Heng ',    # 0x1d
'Cuo ',    # 0x1e
'Lie ',    # 0x1f
'<PERSON> ',    # 0x20
'Ting ',    # 0x21
'Mei ',    # 0x22
'Chun ',    # 0x23
'Shen ',    # 0x24
'Xie ',    # 0x25
'De ',    # 0x26
'Zui ',    # 0x27
'Cu ',    # 0x28
'Xiu ',    # 0x29
'Xin ',    # 0x2a
'Tuo ',    # 0x2b
'Pao ',    # 0x2c
'Cheng ',    # 0x2d
'Nei ',    # 0x2e
'Fu ',    # 0x2f
'Dou ',    # 0x30
'Tuo ',    # 0x31
'Niao ',    # 0x32
'Noy ',    # 0x33
'Pi ',    # 0x34
'Gu ',    # 0x35
'Gua ',    # 0x36
'Li ',    # 0x37
'Lian ',    # 0x38
'Zhang ',    # 0x39
'Cui ',    # 0x3a
'Jie ',    # 0x3b
'Liang ',    # 0x3c
'Zhou ',    # 0x3d
'Pi ',    # 0x3e
'Biao ',    # 0x3f
'Lun ',    # 0x40
'Pian ',    # 0x41
'Guo ',    # 0x42
'Kui ',    # 0x43
'Chui ',    # 0x44
'Dan ',    # 0x45
'Tian ',    # 0x46
'Nei ',    # 0x47
'Jing ',    # 0x48
'Jie ',    # 0x49
'La ',    # 0x4a
'Yi ',    # 0x4b
'An ',    # 0x4c
'Ren ',    # 0x4d
'Shen ',    # 0x4e
'Chuo ',    # 0x4f
'Fu ',    # 0x50
'Fu ',    # 0x51
'Ju ',    # 0x52
'Fei ',    # 0x53
'Qiang ',    # 0x54
'Wan ',    # 0x55
'Dong ',    # 0x56
'Pi ',    # 0x57
'Guo ',    # 0x58
'Zong ',    # 0x59
'Ding ',    # 0x5a
'Wu ',    # 0x5b
'Mei ',    # 0x5c
'Ruan ',    # 0x5d
'Zhuan ',    # 0x5e
'Zhi ',    # 0x5f
'Cou ',    # 0x60
'Gua ',    # 0x61
'Ou ',    # 0x62
'Di ',    # 0x63
'An ',    # 0x64
'Xing ',    # 0x65
'Nao ',    # 0x66
'Yu ',    # 0x67
'Chuan ',    # 0x68
'Nan ',    # 0x69
'Yun ',    # 0x6a
'Zhong ',    # 0x6b
'Rou ',    # 0x6c
'E ',    # 0x6d
'Sai ',    # 0x6e
'Tu ',    # 0x6f
'Yao ',    # 0x70
'Jian ',    # 0x71
'Wei ',    # 0x72
'Jiao ',    # 0x73
'Yu ',    # 0x74
'Jia ',    # 0x75
'Duan ',    # 0x76
'Bi ',    # 0x77
'Chang ',    # 0x78
'Fu ',    # 0x79
'Xian ',    # 0x7a
'Ni ',    # 0x7b
'Mian ',    # 0x7c
'Wa ',    # 0x7d
'Teng ',    # 0x7e
'Tui ',    # 0x7f
'Bang ',    # 0x80
'Qian ',    # 0x81
'Lu ',    # 0x82
'Wa ',    # 0x83
'Sou ',    # 0x84
'Tang ',    # 0x85
'Su ',    # 0x86
'Zhui ',    # 0x87
'Ge ',    # 0x88
'Yi ',    # 0x89
'Bo ',    # 0x8a
'Liao ',    # 0x8b
'Ji ',    # 0x8c
'Pi ',    # 0x8d
'Xie ',    # 0x8e
'Gao ',    # 0x8f
'Lu ',    # 0x90
'Bin ',    # 0x91
'Ou ',    # 0x92
'Chang ',    # 0x93
'Lu ',    # 0x94
'Guo ',    # 0x95
'Pang ',    # 0x96
'Chuai ',    # 0x97
'Piao ',    # 0x98
'Jiang ',    # 0x99
'Fu ',    # 0x9a
'Tang ',    # 0x9b
'Mo ',    # 0x9c
'Xi ',    # 0x9d
'Zhuan ',    # 0x9e
'Lu ',    # 0x9f
'Jiao ',    # 0xa0
'Ying ',    # 0xa1
'Lu ',    # 0xa2
'Zhi ',    # 0xa3
'Tara ',    # 0xa4
'Chun ',    # 0xa5
'Lian ',    # 0xa6
'Tong ',    # 0xa7
'Peng ',    # 0xa8
'Ni ',    # 0xa9
'Zha ',    # 0xaa
'Liao ',    # 0xab
'Cui ',    # 0xac
'Gui ',    # 0xad
'Xiao ',    # 0xae
'Teng ',    # 0xaf
'Fan ',    # 0xb0
'Zhi ',    # 0xb1
'Jiao ',    # 0xb2
'Shan ',    # 0xb3
'Wu ',    # 0xb4
'Cui ',    # 0xb5
'Run ',    # 0xb6
'Xiang ',    # 0xb7
'Sui ',    # 0xb8
'Fen ',    # 0xb9
'Ying ',    # 0xba
'Tan ',    # 0xbb
'Zhua ',    # 0xbc
'Dan ',    # 0xbd
'Kuai ',    # 0xbe
'Nong ',    # 0xbf
'Tun ',    # 0xc0
'Lian ',    # 0xc1
'Bi ',    # 0xc2
'Yong ',    # 0xc3
'Jue ',    # 0xc4
'Chu ',    # 0xc5
'Yi ',    # 0xc6
'Juan ',    # 0xc7
'La ',    # 0xc8
'Lian ',    # 0xc9
'Sao ',    # 0xca
'Tun ',    # 0xcb
'Gu ',    # 0xcc
'Qi ',    # 0xcd
'Cui ',    # 0xce
'Bin ',    # 0xcf
'Xun ',    # 0xd0
'Ru ',    # 0xd1
'Huo ',    # 0xd2
'Zang ',    # 0xd3
'Xian ',    # 0xd4
'Biao ',    # 0xd5
'Xing ',    # 0xd6
'Kuan ',    # 0xd7
'La ',    # 0xd8
'Yan ',    # 0xd9
'Lu ',    # 0xda
'Huo ',    # 0xdb
'Zang ',    # 0xdc
'Luo ',    # 0xdd
'Qu ',    # 0xde
'Zang ',    # 0xdf
'Luan ',    # 0xe0
'Ni ',    # 0xe1
'Zang ',    # 0xe2
'Chen ',    # 0xe3
'Qian ',    # 0xe4
'Wo ',    # 0xe5
'Guang ',    # 0xe6
'Zang ',    # 0xe7
'Lin ',    # 0xe8
'Guang ',    # 0xe9
'Zi ',    # 0xea
'Jiao ',    # 0xeb
'Nie ',    # 0xec
'Chou ',    # 0xed
'Ji ',    # 0xee
'Gao ',    # 0xef
'Chou ',    # 0xf0
'Mian ',    # 0xf1
'Nie ',    # 0xf2
'Zhi ',    # 0xf3
'Zhi ',    # 0xf4
'Ge ',    # 0xf5
'Jian ',    # 0xf6
'Die ',    # 0xf7
'Zhi ',    # 0xf8
'Xiu ',    # 0xf9
'Tai ',    # 0xfa
'Zhen ',    # 0xfb
'Jiu ',    # 0xfc
'Xian ',    # 0xfd
'Yu ',    # 0xfe
'Cha ',    # 0xff
)
