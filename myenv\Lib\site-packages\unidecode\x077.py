data = (
'Ming ',    # 0x00
'Sheng ',    # 0x01
'Shi ',    # 0x02
'Yun ',    # 0x03
'Mian ',    # 0x04
'<PERSON> ',    # 0x05
'<PERSON> ',    # 0x06
'Miao ',    # 0x07
'<PERSON> ',    # 0x08
'<PERSON> ',    # 0x09
'<PERSON> ',    # 0x0a
'Kan ',    # 0x0b
'<PERSON><PERSON> ',    # 0x0c
'Ou ',    # 0x0d
'Shi ',    # 0x0e
'<PERSON> ',    # 0x0f
'<PERSON> ',    # 0x10
'<PERSON> ',    # 0x11
'<PERSON> ',    # 0x12
'Huo ',    # 0x13
'Da ',    # 0x14
'Zhen ',    # 0x15
'Kuang ',    # 0x16
'Ju ',    # 0x17
'Shen ',    # 0x18
'Chi ',    # 0x19
'Sheng ',    # 0x1a
'Mei ',    # 0x1b
'<PERSON> ',    # 0x1c
'Zhu ',    # 0x1d
'Zhen ',    # 0x1e
'Zhen ',    # 0x1f
'Mian ',    # 0x20
'Di ',    # 0x21
'<PERSON> ',    # 0x22
'Die ',    # 0x23
'Yi ',    # 0x24
'Zi ',    # 0x25
'Zi ',    # 0x26
'<PERSON> ',    # 0x27
'Zha ',    # 0x28
'Xuan ',    # 0x29
'Bing ',    # 0x2a
'Mi ',    # 0x2b
'Long ',    # 0x2c
'Sui ',    # 0x2d
'Dong ',    # 0x2e
'Mi ',    # 0x2f
'Die ',    # 0x30
'Yi ',    # 0x31
'Er ',    # 0x32
'Ming ',    # 0x33
'Xuan ',    # 0x34
'Chi ',    # 0x35
'Kuang ',    # 0x36
'Juan ',    # 0x37
'Mou ',    # 0x38
'Zhen ',    # 0x39
'Tiao ',    # 0x3a
'Yang ',    # 0x3b
'Yan ',    # 0x3c
'Mo ',    # 0x3d
'Zhong ',    # 0x3e
'Mai ',    # 0x3f
'Zhao ',    # 0x40
'Zheng ',    # 0x41
'Mei ',    # 0x42
'Jun ',    # 0x43
'Shao ',    # 0x44
'Han ',    # 0x45
'Huan ',    # 0x46
'Di ',    # 0x47
'Cheng ',    # 0x48
'Cuo ',    # 0x49
'Juan ',    # 0x4a
'E ',    # 0x4b
'Wan ',    # 0x4c
'Xian ',    # 0x4d
'Xi ',    # 0x4e
'Kun ',    # 0x4f
'Lai ',    # 0x50
'Jian ',    # 0x51
'Shan ',    # 0x52
'Tian ',    # 0x53
'Hun ',    # 0x54
'Wan ',    # 0x55
'Ling ',    # 0x56
'Shi ',    # 0x57
'Qiong ',    # 0x58
'Lie ',    # 0x59
'Yai ',    # 0x5a
'Jing ',    # 0x5b
'Zheng ',    # 0x5c
'Li ',    # 0x5d
'Lai ',    # 0x5e
'Sui ',    # 0x5f
'Juan ',    # 0x60
'Shui ',    # 0x61
'Sui ',    # 0x62
'Du ',    # 0x63
'Bi ',    # 0x64
'Bi ',    # 0x65
'Mu ',    # 0x66
'Hun ',    # 0x67
'Ni ',    # 0x68
'Lu ',    # 0x69
'Yi ',    # 0x6a
'Jie ',    # 0x6b
'Cai ',    # 0x6c
'Zhou ',    # 0x6d
'Yu ',    # 0x6e
'Hun ',    # 0x6f
'Ma ',    # 0x70
'Xia ',    # 0x71
'Xing ',    # 0x72
'Xi ',    # 0x73
'Gun ',    # 0x74
'Cai ',    # 0x75
'Chun ',    # 0x76
'Jian ',    # 0x77
'Mei ',    # 0x78
'Du ',    # 0x79
'Hou ',    # 0x7a
'Xuan ',    # 0x7b
'Ti ',    # 0x7c
'Kui ',    # 0x7d
'Gao ',    # 0x7e
'Rui ',    # 0x7f
'Mou ',    # 0x80
'Xu ',    # 0x81
'Fa ',    # 0x82
'Wen ',    # 0x83
'Miao ',    # 0x84
'Chou ',    # 0x85
'Kui ',    # 0x86
'Mi ',    # 0x87
'Weng ',    # 0x88
'Kou ',    # 0x89
'Dang ',    # 0x8a
'Chen ',    # 0x8b
'Ke ',    # 0x8c
'Sou ',    # 0x8d
'Xia ',    # 0x8e
'Qiong ',    # 0x8f
'Mao ',    # 0x90
'Ming ',    # 0x91
'Man ',    # 0x92
'Shui ',    # 0x93
'Ze ',    # 0x94
'Zhang ',    # 0x95
'Yi ',    # 0x96
'Diao ',    # 0x97
'Ou ',    # 0x98
'Mo ',    # 0x99
'Shun ',    # 0x9a
'Cong ',    # 0x9b
'Lou ',    # 0x9c
'Chi ',    # 0x9d
'Man ',    # 0x9e
'Piao ',    # 0x9f
'Cheng ',    # 0xa0
'Ji ',    # 0xa1
'Meng ',    # 0xa2
None,    # 0xa3
'Run ',    # 0xa4
'Pie ',    # 0xa5
'Xi ',    # 0xa6
'Qiao ',    # 0xa7
'Pu ',    # 0xa8
'Zhu ',    # 0xa9
'Deng ',    # 0xaa
'Shen ',    # 0xab
'Shun ',    # 0xac
'Liao ',    # 0xad
'Che ',    # 0xae
'Xian ',    # 0xaf
'Kan ',    # 0xb0
'Ye ',    # 0xb1
'Xu ',    # 0xb2
'Tong ',    # 0xb3
'Mou ',    # 0xb4
'Lin ',    # 0xb5
'Kui ',    # 0xb6
'Xian ',    # 0xb7
'Ye ',    # 0xb8
'Ai ',    # 0xb9
'Hui ',    # 0xba
'Zhan ',    # 0xbb
'Jian ',    # 0xbc
'Gu ',    # 0xbd
'Zhao ',    # 0xbe
'Qu ',    # 0xbf
'Wei ',    # 0xc0
'Chou ',    # 0xc1
'Sao ',    # 0xc2
'Ning ',    # 0xc3
'Xun ',    # 0xc4
'Yao ',    # 0xc5
'Huo ',    # 0xc6
'Meng ',    # 0xc7
'Mian ',    # 0xc8
'Bin ',    # 0xc9
'Mian ',    # 0xca
'Li ',    # 0xcb
'Kuang ',    # 0xcc
'Jue ',    # 0xcd
'Xuan ',    # 0xce
'Mian ',    # 0xcf
'Huo ',    # 0xd0
'Lu ',    # 0xd1
'Meng ',    # 0xd2
'Long ',    # 0xd3
'Guan ',    # 0xd4
'Man ',    # 0xd5
'Xi ',    # 0xd6
'Chu ',    # 0xd7
'Tang ',    # 0xd8
'Kan ',    # 0xd9
'Zhu ',    # 0xda
'Mao ',    # 0xdb
'Jin ',    # 0xdc
'Lin ',    # 0xdd
'Yu ',    # 0xde
'Shuo ',    # 0xdf
'Ce ',    # 0xe0
'Jue ',    # 0xe1
'Shi ',    # 0xe2
'Yi ',    # 0xe3
'Shen ',    # 0xe4
'Zhi ',    # 0xe5
'Hou ',    # 0xe6
'Shen ',    # 0xe7
'Ying ',    # 0xe8
'Ju ',    # 0xe9
'Zhou ',    # 0xea
'Jiao ',    # 0xeb
'Cuo ',    # 0xec
'Duan ',    # 0xed
'Ai ',    # 0xee
'Jiao ',    # 0xef
'Zeng ',    # 0xf0
'Huo ',    # 0xf1
'Bai ',    # 0xf2
'Shi ',    # 0xf3
'Ding ',    # 0xf4
'Qi ',    # 0xf5
'Ji ',    # 0xf6
'Zi ',    # 0xf7
'Gan ',    # 0xf8
'Wu ',    # 0xf9
'Tuo ',    # 0xfa
'Ku ',    # 0xfb
'Qiang ',    # 0xfc
'Xi ',    # 0xfd
'Fan ',    # 0xfe
'Kuang ',    # 0xff
)
