data = (
'Sha ',    # 0x00
'Li ',    # 0x01
'Han ',    # 0x02
'<PERSON>an ',    # 0x03
'Jing ',    # 0x04
'Pai ',    # 0x05
'Fei ',    # 0x06
'<PERSON> ',    # 0x07
'Ba ',    # 0x08
'Qi ',    # 0x09
'Ni ',    # 0x0a
'Biao ',    # 0x0b
'<PERSON> ',    # 0x0c
'Lai ',    # 0x0d
'Xi ',    # 0x0e
'Jian ',    # 0x0f
'<PERSON>ang ',    # 0x10
'Kun ',    # 0x11
'Yan ',    # 0x12
'<PERSON> ',    # 0x13
'Zong ',    # 0x14
'Mi ',    # 0x15
'Chang ',    # 0x16
'Yi ',    # 0x17
'Zhi ',    # 0x18
'Zheng ',    # 0x19
'Ya ',    # 0x1a
'Meng ',    # 0x1b
'Cai ',    # 0x1c
'Cu ',    # 0x1d
'She ',    # 0x1e
'Kari ',    # 0x1f
'Cen ',    # 0x20
'Luo ',    # 0x21
'Hu ',    # 0x22
'Zong ',    # 0x23
'Ji ',    # 0x24
'Wei ',    # 0x25
'<PERSON> ',    # 0x26
'Wo ',    # 0x27
'Yuan ',    # 0x28
'Xing ',    # 0x29
'Zhu ',    # 0x2a
'Mao ',    # 0x2b
'Wei ',    # 0x2c
'Yuan ',    # 0x2d
'Xian ',    # 0x2e
'Tuan ',    # 0x2f
'Ya ',    # 0x30
'Nao ',    # 0x31
'Xie ',    # 0x32
'Jia ',    # 0x33
'Hou ',    # 0x34
'Bian ',    # 0x35
'You ',    # 0x36
'You ',    # 0x37
'Mei ',    # 0x38
'Zha ',    # 0x39
'Yao ',    # 0x3a
'Sun ',    # 0x3b
'Bo ',    # 0x3c
'Ming ',    # 0x3d
'Hua ',    # 0x3e
'Yuan ',    # 0x3f
'Sou ',    # 0x40
'Ma ',    # 0x41
'Yuan ',    # 0x42
'Dai ',    # 0x43
'Yu ',    # 0x44
'Shi ',    # 0x45
'Hao ',    # 0x46
None,    # 0x47
'Yi ',    # 0x48
'Zhen ',    # 0x49
'Chuang ',    # 0x4a
'Hao ',    # 0x4b
'Man ',    # 0x4c
'Jing ',    # 0x4d
'Jiang ',    # 0x4e
'Mu ',    # 0x4f
'Zhang ',    # 0x50
'Chan ',    # 0x51
'Ao ',    # 0x52
'Ao ',    # 0x53
'Hao ',    # 0x54
'Cui ',    # 0x55
'Fen ',    # 0x56
'Jue ',    # 0x57
'Bi ',    # 0x58
'Bi ',    # 0x59
'Huang ',    # 0x5a
'Pu ',    # 0x5b
'Lin ',    # 0x5c
'Yu ',    # 0x5d
'Tong ',    # 0x5e
'Yao ',    # 0x5f
'Liao ',    # 0x60
'Shuo ',    # 0x61
'Xiao ',    # 0x62
'Swu ',    # 0x63
'Ton ',    # 0x64
'Xi ',    # 0x65
'Ge ',    # 0x66
'Juan ',    # 0x67
'Du ',    # 0x68
'Hui ',    # 0x69
'Kuai ',    # 0x6a
'Xian ',    # 0x6b
'Xie ',    # 0x6c
'Ta ',    # 0x6d
'Xian ',    # 0x6e
'Xun ',    # 0x6f
'Ning ',    # 0x70
'Pin ',    # 0x71
'Huo ',    # 0x72
'Nou ',    # 0x73
'Meng ',    # 0x74
'Lie ',    # 0x75
'Nao ',    # 0x76
'Guang ',    # 0x77
'Shou ',    # 0x78
'Lu ',    # 0x79
'Ta ',    # 0x7a
'Xian ',    # 0x7b
'Mi ',    # 0x7c
'Rang ',    # 0x7d
'Huan ',    # 0x7e
'Nao ',    # 0x7f
'Luo ',    # 0x80
'Xian ',    # 0x81
'Qi ',    # 0x82
'Jue ',    # 0x83
'Xuan ',    # 0x84
'Miao ',    # 0x85
'Zi ',    # 0x86
'Lu ',    # 0x87
'Lu ',    # 0x88
'Yu ',    # 0x89
'Su ',    # 0x8a
'Wang ',    # 0x8b
'Qiu ',    # 0x8c
'Ga ',    # 0x8d
'Ding ',    # 0x8e
'Le ',    # 0x8f
'Ba ',    # 0x90
'Ji ',    # 0x91
'Hong ',    # 0x92
'Di ',    # 0x93
'Quan ',    # 0x94
'Gan ',    # 0x95
'Jiu ',    # 0x96
'Yu ',    # 0x97
'Ji ',    # 0x98
'Yu ',    # 0x99
'Yang ',    # 0x9a
'Ma ',    # 0x9b
'Gong ',    # 0x9c
'Wu ',    # 0x9d
'Fu ',    # 0x9e
'Wen ',    # 0x9f
'Jie ',    # 0xa0
'Ya ',    # 0xa1
'Fen ',    # 0xa2
'Bian ',    # 0xa3
'Beng ',    # 0xa4
'Yue ',    # 0xa5
'Jue ',    # 0xa6
'Yun ',    # 0xa7
'Jue ',    # 0xa8
'Wan ',    # 0xa9
'Jian ',    # 0xaa
'Mei ',    # 0xab
'Dan ',    # 0xac
'Pi ',    # 0xad
'Wei ',    # 0xae
'Huan ',    # 0xaf
'Xian ',    # 0xb0
'Qiang ',    # 0xb1
'Ling ',    # 0xb2
'Dai ',    # 0xb3
'Yi ',    # 0xb4
'An ',    # 0xb5
'Ping ',    # 0xb6
'Dian ',    # 0xb7
'Fu ',    # 0xb8
'Xuan ',    # 0xb9
'Xi ',    # 0xba
'Bo ',    # 0xbb
'Ci ',    # 0xbc
'Gou ',    # 0xbd
'Jia ',    # 0xbe
'Shao ',    # 0xbf
'Po ',    # 0xc0
'Ci ',    # 0xc1
'Ke ',    # 0xc2
'Ran ',    # 0xc3
'Sheng ',    # 0xc4
'Shen ',    # 0xc5
'Yi ',    # 0xc6
'Zu ',    # 0xc7
'Jia ',    # 0xc8
'Min ',    # 0xc9
'Shan ',    # 0xca
'Liu ',    # 0xcb
'Bi ',    # 0xcc
'Zhen ',    # 0xcd
'Zhen ',    # 0xce
'Jue ',    # 0xcf
'Fa ',    # 0xd0
'Long ',    # 0xd1
'Jin ',    # 0xd2
'Jiao ',    # 0xd3
'Jian ',    # 0xd4
'Li ',    # 0xd5
'Guang ',    # 0xd6
'Xian ',    # 0xd7
'Zhou ',    # 0xd8
'Gong ',    # 0xd9
'Yan ',    # 0xda
'Xiu ',    # 0xdb
'Yang ',    # 0xdc
'Xu ',    # 0xdd
'Luo ',    # 0xde
'Su ',    # 0xdf
'Zhu ',    # 0xe0
'Qin ',    # 0xe1
'Ken ',    # 0xe2
'Xun ',    # 0xe3
'Bao ',    # 0xe4
'Er ',    # 0xe5
'Xiang ',    # 0xe6
'Yao ',    # 0xe7
'Xia ',    # 0xe8
'Heng ',    # 0xe9
'Gui ',    # 0xea
'Chong ',    # 0xeb
'Xu ',    # 0xec
'Ban ',    # 0xed
'Pei ',    # 0xee
None,    # 0xef
'Dang ',    # 0xf0
'Ei ',    # 0xf1
'Hun ',    # 0xf2
'Wen ',    # 0xf3
'E ',    # 0xf4
'Cheng ',    # 0xf5
'Ti ',    # 0xf6
'Wu ',    # 0xf7
'Wu ',    # 0xf8
'Cheng ',    # 0xf9
'Jun ',    # 0xfa
'Mei ',    # 0xfb
'Bei ',    # 0xfc
'Ting ',    # 0xfd
'Xian ',    # 0xfe
'Chuo ',    # 0xff
)
