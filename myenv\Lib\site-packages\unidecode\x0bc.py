data = (
'mil',    # 0x00
'milg',    # 0x01
'milm',    # 0x02
'milb',    # 0x03
'mils',    # 0x04
'milt',    # 0x05
'milp',    # 0x06
'milh',    # 0x07
'mim',    # 0x08
'mib',    # 0x09
'mibs',    # 0x0a
'mis',    # 0x0b
'miss',    # 0x0c
'ming',    # 0x0d
'mij',    # 0x0e
'mic',    # 0x0f
'mik',    # 0x10
'mit',    # 0x11
'mip',    # 0x12
'mih',    # 0x13
'ba',    # 0x14
'bag',    # 0x15
'bagg',    # 0x16
'bags',    # 0x17
'ban',    # 0x18
'banj',    # 0x19
'banh',    # 0x1a
'bad',    # 0x1b
'bal',    # 0x1c
'balg',    # 0x1d
'balm',    # 0x1e
'balb',    # 0x1f
'bals',    # 0x20
'balt',    # 0x21
'balp',    # 0x22
'balh',    # 0x23
'bam',    # 0x24
'bab',    # 0x25
'babs',    # 0x26
'bas',    # 0x27
'bass',    # 0x28
'bang',    # 0x29
'baj',    # 0x2a
'bac',    # 0x2b
'bak',    # 0x2c
'bat',    # 0x2d
'bap',    # 0x2e
'bah',    # 0x2f
'bae',    # 0x30
'baeg',    # 0x31
'baegg',    # 0x32
'baegs',    # 0x33
'baen',    # 0x34
'baenj',    # 0x35
'baenh',    # 0x36
'baed',    # 0x37
'bael',    # 0x38
'baelg',    # 0x39
'baelm',    # 0x3a
'baelb',    # 0x3b
'baels',    # 0x3c
'baelt',    # 0x3d
'baelp',    # 0x3e
'baelh',    # 0x3f
'baem',    # 0x40
'baeb',    # 0x41
'baebs',    # 0x42
'baes',    # 0x43
'baess',    # 0x44
'baeng',    # 0x45
'baej',    # 0x46
'baec',    # 0x47
'baek',    # 0x48
'baet',    # 0x49
'baep',    # 0x4a
'baeh',    # 0x4b
'bya',    # 0x4c
'byag',    # 0x4d
'byagg',    # 0x4e
'byags',    # 0x4f
'byan',    # 0x50
'byanj',    # 0x51
'byanh',    # 0x52
'byad',    # 0x53
'byal',    # 0x54
'byalg',    # 0x55
'byalm',    # 0x56
'byalb',    # 0x57
'byals',    # 0x58
'byalt',    # 0x59
'byalp',    # 0x5a
'byalh',    # 0x5b
'byam',    # 0x5c
'byab',    # 0x5d
'byabs',    # 0x5e
'byas',    # 0x5f
'byass',    # 0x60
'byang',    # 0x61
'byaj',    # 0x62
'byac',    # 0x63
'byak',    # 0x64
'byat',    # 0x65
'byap',    # 0x66
'byah',    # 0x67
'byae',    # 0x68
'byaeg',    # 0x69
'byaegg',    # 0x6a
'byaegs',    # 0x6b
'byaen',    # 0x6c
'byaenj',    # 0x6d
'byaenh',    # 0x6e
'byaed',    # 0x6f
'byael',    # 0x70
'byaelg',    # 0x71
'byaelm',    # 0x72
'byaelb',    # 0x73
'byaels',    # 0x74
'byaelt',    # 0x75
'byaelp',    # 0x76
'byaelh',    # 0x77
'byaem',    # 0x78
'byaeb',    # 0x79
'byaebs',    # 0x7a
'byaes',    # 0x7b
'byaess',    # 0x7c
'byaeng',    # 0x7d
'byaej',    # 0x7e
'byaec',    # 0x7f
'byaek',    # 0x80
'byaet',    # 0x81
'byaep',    # 0x82
'byaeh',    # 0x83
'beo',    # 0x84
'beog',    # 0x85
'beogg',    # 0x86
'beogs',    # 0x87
'beon',    # 0x88
'beonj',    # 0x89
'beonh',    # 0x8a
'beod',    # 0x8b
'beol',    # 0x8c
'beolg',    # 0x8d
'beolm',    # 0x8e
'beolb',    # 0x8f
'beols',    # 0x90
'beolt',    # 0x91
'beolp',    # 0x92
'beolh',    # 0x93
'beom',    # 0x94
'beob',    # 0x95
'beobs',    # 0x96
'beos',    # 0x97
'beoss',    # 0x98
'beong',    # 0x99
'beoj',    # 0x9a
'beoc',    # 0x9b
'beok',    # 0x9c
'beot',    # 0x9d
'beop',    # 0x9e
'beoh',    # 0x9f
'be',    # 0xa0
'beg',    # 0xa1
'begg',    # 0xa2
'begs',    # 0xa3
'ben',    # 0xa4
'benj',    # 0xa5
'benh',    # 0xa6
'bed',    # 0xa7
'bel',    # 0xa8
'belg',    # 0xa9
'belm',    # 0xaa
'belb',    # 0xab
'bels',    # 0xac
'belt',    # 0xad
'belp',    # 0xae
'belh',    # 0xaf
'bem',    # 0xb0
'beb',    # 0xb1
'bebs',    # 0xb2
'bes',    # 0xb3
'bess',    # 0xb4
'beng',    # 0xb5
'bej',    # 0xb6
'bec',    # 0xb7
'bek',    # 0xb8
'bet',    # 0xb9
'bep',    # 0xba
'beh',    # 0xbb
'byeo',    # 0xbc
'byeog',    # 0xbd
'byeogg',    # 0xbe
'byeogs',    # 0xbf
'byeon',    # 0xc0
'byeonj',    # 0xc1
'byeonh',    # 0xc2
'byeod',    # 0xc3
'byeol',    # 0xc4
'byeolg',    # 0xc5
'byeolm',    # 0xc6
'byeolb',    # 0xc7
'byeols',    # 0xc8
'byeolt',    # 0xc9
'byeolp',    # 0xca
'byeolh',    # 0xcb
'byeom',    # 0xcc
'byeob',    # 0xcd
'byeobs',    # 0xce
'byeos',    # 0xcf
'byeoss',    # 0xd0
'byeong',    # 0xd1
'byeoj',    # 0xd2
'byeoc',    # 0xd3
'byeok',    # 0xd4
'byeot',    # 0xd5
'byeop',    # 0xd6
'byeoh',    # 0xd7
'bye',    # 0xd8
'byeg',    # 0xd9
'byegg',    # 0xda
'byegs',    # 0xdb
'byen',    # 0xdc
'byenj',    # 0xdd
'byenh',    # 0xde
'byed',    # 0xdf
'byel',    # 0xe0
'byelg',    # 0xe1
'byelm',    # 0xe2
'byelb',    # 0xe3
'byels',    # 0xe4
'byelt',    # 0xe5
'byelp',    # 0xe6
'byelh',    # 0xe7
'byem',    # 0xe8
'byeb',    # 0xe9
'byebs',    # 0xea
'byes',    # 0xeb
'byess',    # 0xec
'byeng',    # 0xed
'byej',    # 0xee
'byec',    # 0xef
'byek',    # 0xf0
'byet',    # 0xf1
'byep',    # 0xf2
'byeh',    # 0xf3
'bo',    # 0xf4
'bog',    # 0xf5
'bogg',    # 0xf6
'bogs',    # 0xf7
'bon',    # 0xf8
'bonj',    # 0xf9
'bonh',    # 0xfa
'bod',    # 0xfb
'bol',    # 0xfc
'bolg',    # 0xfd
'bolm',    # 0xfe
'bolb',    # 0xff
)
