data = (
'poss',    # 0x00
'pong',    # 0x01
'poj',    # 0x02
'poc',    # 0x03
'pok',    # 0x04
'pot',    # 0x05
'pop',    # 0x06
'poh',    # 0x07
'pwa',    # 0x08
'pwag',    # 0x09
'pwagg',    # 0x0a
'pwags',    # 0x0b
'pwan',    # 0x0c
'pwanj',    # 0x0d
'pwanh',    # 0x0e
'pwad',    # 0x0f
'pwal',    # 0x10
'pwalg',    # 0x11
'pwalm',    # 0x12
'pwalb',    # 0x13
'pwals',    # 0x14
'pwalt',    # 0x15
'pwalp',    # 0x16
'pwalh',    # 0x17
'pwam',    # 0x18
'pwab',    # 0x19
'pwabs',    # 0x1a
'pwas',    # 0x1b
'pwass',    # 0x1c
'pwang',    # 0x1d
'pwaj',    # 0x1e
'pwac',    # 0x1f
'pwak',    # 0x20
'pwat',    # 0x21
'pwap',    # 0x22
'pwah',    # 0x23
'pwae',    # 0x24
'pwaeg',    # 0x25
'pwaegg',    # 0x26
'pwaegs',    # 0x27
'pwaen',    # 0x28
'pwaenj',    # 0x29
'pwaenh',    # 0x2a
'pwaed',    # 0x2b
'pwael',    # 0x2c
'pwaelg',    # 0x2d
'pwaelm',    # 0x2e
'pwaelb',    # 0x2f
'pwaels',    # 0x30
'pwaelt',    # 0x31
'pwaelp',    # 0x32
'pwaelh',    # 0x33
'pwaem',    # 0x34
'pwaeb',    # 0x35
'pwaebs',    # 0x36
'pwaes',    # 0x37
'pwaess',    # 0x38
'pwaeng',    # 0x39
'pwaej',    # 0x3a
'pwaec',    # 0x3b
'pwaek',    # 0x3c
'pwaet',    # 0x3d
'pwaep',    # 0x3e
'pwaeh',    # 0x3f
'poe',    # 0x40
'poeg',    # 0x41
'poegg',    # 0x42
'poegs',    # 0x43
'poen',    # 0x44
'poenj',    # 0x45
'poenh',    # 0x46
'poed',    # 0x47
'poel',    # 0x48
'poelg',    # 0x49
'poelm',    # 0x4a
'poelb',    # 0x4b
'poels',    # 0x4c
'poelt',    # 0x4d
'poelp',    # 0x4e
'poelh',    # 0x4f
'poem',    # 0x50
'poeb',    # 0x51
'poebs',    # 0x52
'poes',    # 0x53
'poess',    # 0x54
'poeng',    # 0x55
'poej',    # 0x56
'poec',    # 0x57
'poek',    # 0x58
'poet',    # 0x59
'poep',    # 0x5a
'poeh',    # 0x5b
'pyo',    # 0x5c
'pyog',    # 0x5d
'pyogg',    # 0x5e
'pyogs',    # 0x5f
'pyon',    # 0x60
'pyonj',    # 0x61
'pyonh',    # 0x62
'pyod',    # 0x63
'pyol',    # 0x64
'pyolg',    # 0x65
'pyolm',    # 0x66
'pyolb',    # 0x67
'pyols',    # 0x68
'pyolt',    # 0x69
'pyolp',    # 0x6a
'pyolh',    # 0x6b
'pyom',    # 0x6c
'pyob',    # 0x6d
'pyobs',    # 0x6e
'pyos',    # 0x6f
'pyoss',    # 0x70
'pyong',    # 0x71
'pyoj',    # 0x72
'pyoc',    # 0x73
'pyok',    # 0x74
'pyot',    # 0x75
'pyop',    # 0x76
'pyoh',    # 0x77
'pu',    # 0x78
'pug',    # 0x79
'pugg',    # 0x7a
'pugs',    # 0x7b
'pun',    # 0x7c
'punj',    # 0x7d
'punh',    # 0x7e
'pud',    # 0x7f
'pul',    # 0x80
'pulg',    # 0x81
'pulm',    # 0x82
'pulb',    # 0x83
'puls',    # 0x84
'pult',    # 0x85
'pulp',    # 0x86
'pulh',    # 0x87
'pum',    # 0x88
'pub',    # 0x89
'pubs',    # 0x8a
'pus',    # 0x8b
'puss',    # 0x8c
'pung',    # 0x8d
'puj',    # 0x8e
'puc',    # 0x8f
'puk',    # 0x90
'put',    # 0x91
'pup',    # 0x92
'puh',    # 0x93
'pweo',    # 0x94
'pweog',    # 0x95
'pweogg',    # 0x96
'pweogs',    # 0x97
'pweon',    # 0x98
'pweonj',    # 0x99
'pweonh',    # 0x9a
'pweod',    # 0x9b
'pweol',    # 0x9c
'pweolg',    # 0x9d
'pweolm',    # 0x9e
'pweolb',    # 0x9f
'pweols',    # 0xa0
'pweolt',    # 0xa1
'pweolp',    # 0xa2
'pweolh',    # 0xa3
'pweom',    # 0xa4
'pweob',    # 0xa5
'pweobs',    # 0xa6
'pweos',    # 0xa7
'pweoss',    # 0xa8
'pweong',    # 0xa9
'pweoj',    # 0xaa
'pweoc',    # 0xab
'pweok',    # 0xac
'pweot',    # 0xad
'pweop',    # 0xae
'pweoh',    # 0xaf
'pwe',    # 0xb0
'pweg',    # 0xb1
'pwegg',    # 0xb2
'pwegs',    # 0xb3
'pwen',    # 0xb4
'pwenj',    # 0xb5
'pwenh',    # 0xb6
'pwed',    # 0xb7
'pwel',    # 0xb8
'pwelg',    # 0xb9
'pwelm',    # 0xba
'pwelb',    # 0xbb
'pwels',    # 0xbc
'pwelt',    # 0xbd
'pwelp',    # 0xbe
'pwelh',    # 0xbf
'pwem',    # 0xc0
'pweb',    # 0xc1
'pwebs',    # 0xc2
'pwes',    # 0xc3
'pwess',    # 0xc4
'pweng',    # 0xc5
'pwej',    # 0xc6
'pwec',    # 0xc7
'pwek',    # 0xc8
'pwet',    # 0xc9
'pwep',    # 0xca
'pweh',    # 0xcb
'pwi',    # 0xcc
'pwig',    # 0xcd
'pwigg',    # 0xce
'pwigs',    # 0xcf
'pwin',    # 0xd0
'pwinj',    # 0xd1
'pwinh',    # 0xd2
'pwid',    # 0xd3
'pwil',    # 0xd4
'pwilg',    # 0xd5
'pwilm',    # 0xd6
'pwilb',    # 0xd7
'pwils',    # 0xd8
'pwilt',    # 0xd9
'pwilp',    # 0xda
'pwilh',    # 0xdb
'pwim',    # 0xdc
'pwib',    # 0xdd
'pwibs',    # 0xde
'pwis',    # 0xdf
'pwiss',    # 0xe0
'pwing',    # 0xe1
'pwij',    # 0xe2
'pwic',    # 0xe3
'pwik',    # 0xe4
'pwit',    # 0xe5
'pwip',    # 0xe6
'pwih',    # 0xe7
'pyu',    # 0xe8
'pyug',    # 0xe9
'pyugg',    # 0xea
'pyugs',    # 0xeb
'pyun',    # 0xec
'pyunj',    # 0xed
'pyunh',    # 0xee
'pyud',    # 0xef
'pyul',    # 0xf0
'pyulg',    # 0xf1
'pyulm',    # 0xf2
'pyulb',    # 0xf3
'pyuls',    # 0xf4
'pyult',    # 0xf5
'pyulp',    # 0xf6
'pyulh',    # 0xf7
'pyum',    # 0xf8
'pyub',    # 0xf9
'pyubs',    # 0xfa
'pyus',    # 0xfb
'pyuss',    # 0xfc
'pyung',    # 0xfd
'pyuj',    # 0xfe
'pyuc',    # 0xff
)
