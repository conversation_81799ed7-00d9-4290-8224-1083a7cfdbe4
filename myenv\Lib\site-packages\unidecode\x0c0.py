data = (
'bbweok',    # 0x00
'bbweot',    # 0x01
'bbweop',    # 0x02
'bbweoh',    # 0x03
'bbwe',    # 0x04
'bbweg',    # 0x05
'bbwegg',    # 0x06
'bbwegs',    # 0x07
'bbwen',    # 0x08
'bbwenj',    # 0x09
'bbwenh',    # 0x0a
'bbwed',    # 0x0b
'bbwel',    # 0x0c
'bbwelg',    # 0x0d
'bbwelm',    # 0x0e
'bbwelb',    # 0x0f
'bbwels',    # 0x10
'bbwelt',    # 0x11
'bbwelp',    # 0x12
'bbwelh',    # 0x13
'bbwem',    # 0x14
'bbweb',    # 0x15
'bbwebs',    # 0x16
'bbwes',    # 0x17
'bbwess',    # 0x18
'bbweng',    # 0x19
'bbwej',    # 0x1a
'bbwec',    # 0x1b
'bbwek',    # 0x1c
'bbwet',    # 0x1d
'bbwep',    # 0x1e
'bbweh',    # 0x1f
'bbwi',    # 0x20
'bbwig',    # 0x21
'bbwigg',    # 0x22
'bbwigs',    # 0x23
'bbwin',    # 0x24
'bbwinj',    # 0x25
'bbwinh',    # 0x26
'bbwid',    # 0x27
'bbwil',    # 0x28
'bbwilg',    # 0x29
'bbwilm',    # 0x2a
'bbwilb',    # 0x2b
'bbwils',    # 0x2c
'bbwilt',    # 0x2d
'bbwilp',    # 0x2e
'bbwilh',    # 0x2f
'bbwim',    # 0x30
'bbwib',    # 0x31
'bbwibs',    # 0x32
'bbwis',    # 0x33
'bbwiss',    # 0x34
'bbwing',    # 0x35
'bbwij',    # 0x36
'bbwic',    # 0x37
'bbwik',    # 0x38
'bbwit',    # 0x39
'bbwip',    # 0x3a
'bbwih',    # 0x3b
'bbyu',    # 0x3c
'bbyug',    # 0x3d
'bbyugg',    # 0x3e
'bbyugs',    # 0x3f
'bbyun',    # 0x40
'bbyunj',    # 0x41
'bbyunh',    # 0x42
'bbyud',    # 0x43
'bbyul',    # 0x44
'bbyulg',    # 0x45
'bbyulm',    # 0x46
'bbyulb',    # 0x47
'bbyuls',    # 0x48
'bbyult',    # 0x49
'bbyulp',    # 0x4a
'bbyulh',    # 0x4b
'bbyum',    # 0x4c
'bbyub',    # 0x4d
'bbyubs',    # 0x4e
'bbyus',    # 0x4f
'bbyuss',    # 0x50
'bbyung',    # 0x51
'bbyuj',    # 0x52
'bbyuc',    # 0x53
'bbyuk',    # 0x54
'bbyut',    # 0x55
'bbyup',    # 0x56
'bbyuh',    # 0x57
'bbeu',    # 0x58
'bbeug',    # 0x59
'bbeugg',    # 0x5a
'bbeugs',    # 0x5b
'bbeun',    # 0x5c
'bbeunj',    # 0x5d
'bbeunh',    # 0x5e
'bbeud',    # 0x5f
'bbeul',    # 0x60
'bbeulg',    # 0x61
'bbeulm',    # 0x62
'bbeulb',    # 0x63
'bbeuls',    # 0x64
'bbeult',    # 0x65
'bbeulp',    # 0x66
'bbeulh',    # 0x67
'bbeum',    # 0x68
'bbeub',    # 0x69
'bbeubs',    # 0x6a
'bbeus',    # 0x6b
'bbeuss',    # 0x6c
'bbeung',    # 0x6d
'bbeuj',    # 0x6e
'bbeuc',    # 0x6f
'bbeuk',    # 0x70
'bbeut',    # 0x71
'bbeup',    # 0x72
'bbeuh',    # 0x73
'bbyi',    # 0x74
'bbyig',    # 0x75
'bbyigg',    # 0x76
'bbyigs',    # 0x77
'bbyin',    # 0x78
'bbyinj',    # 0x79
'bbyinh',    # 0x7a
'bbyid',    # 0x7b
'bbyil',    # 0x7c
'bbyilg',    # 0x7d
'bbyilm',    # 0x7e
'bbyilb',    # 0x7f
'bbyils',    # 0x80
'bbyilt',    # 0x81
'bbyilp',    # 0x82
'bbyilh',    # 0x83
'bbyim',    # 0x84
'bbyib',    # 0x85
'bbyibs',    # 0x86
'bbyis',    # 0x87
'bbyiss',    # 0x88
'bbying',    # 0x89
'bbyij',    # 0x8a
'bbyic',    # 0x8b
'bbyik',    # 0x8c
'bbyit',    # 0x8d
'bbyip',    # 0x8e
'bbyih',    # 0x8f
'bbi',    # 0x90
'bbig',    # 0x91
'bbigg',    # 0x92
'bbigs',    # 0x93
'bbin',    # 0x94
'bbinj',    # 0x95
'bbinh',    # 0x96
'bbid',    # 0x97
'bbil',    # 0x98
'bbilg',    # 0x99
'bbilm',    # 0x9a
'bbilb',    # 0x9b
'bbils',    # 0x9c
'bbilt',    # 0x9d
'bbilp',    # 0x9e
'bbilh',    # 0x9f
'bbim',    # 0xa0
'bbib',    # 0xa1
'bbibs',    # 0xa2
'bbis',    # 0xa3
'bbiss',    # 0xa4
'bbing',    # 0xa5
'bbij',    # 0xa6
'bbic',    # 0xa7
'bbik',    # 0xa8
'bbit',    # 0xa9
'bbip',    # 0xaa
'bbih',    # 0xab
'sa',    # 0xac
'sag',    # 0xad
'sagg',    # 0xae
'sags',    # 0xaf
'san',    # 0xb0
'sanj',    # 0xb1
'sanh',    # 0xb2
'sad',    # 0xb3
'sal',    # 0xb4
'salg',    # 0xb5
'salm',    # 0xb6
'salb',    # 0xb7
'sals',    # 0xb8
'salt',    # 0xb9
'salp',    # 0xba
'salh',    # 0xbb
'sam',    # 0xbc
'sab',    # 0xbd
'sabs',    # 0xbe
'sas',    # 0xbf
'sass',    # 0xc0
'sang',    # 0xc1
'saj',    # 0xc2
'sac',    # 0xc3
'sak',    # 0xc4
'sat',    # 0xc5
'sap',    # 0xc6
'sah',    # 0xc7
'sae',    # 0xc8
'saeg',    # 0xc9
'saegg',    # 0xca
'saegs',    # 0xcb
'saen',    # 0xcc
'saenj',    # 0xcd
'saenh',    # 0xce
'saed',    # 0xcf
'sael',    # 0xd0
'saelg',    # 0xd1
'saelm',    # 0xd2
'saelb',    # 0xd3
'saels',    # 0xd4
'saelt',    # 0xd5
'saelp',    # 0xd6
'saelh',    # 0xd7
'saem',    # 0xd8
'saeb',    # 0xd9
'saebs',    # 0xda
'saes',    # 0xdb
'saess',    # 0xdc
'saeng',    # 0xdd
'saej',    # 0xde
'saec',    # 0xdf
'saek',    # 0xe0
'saet',    # 0xe1
'saep',    # 0xe2
'saeh',    # 0xe3
'sya',    # 0xe4
'syag',    # 0xe5
'syagg',    # 0xe6
'syags',    # 0xe7
'syan',    # 0xe8
'syanj',    # 0xe9
'syanh',    # 0xea
'syad',    # 0xeb
'syal',    # 0xec
'syalg',    # 0xed
'syalm',    # 0xee
'syalb',    # 0xef
'syals',    # 0xf0
'syalt',    # 0xf1
'syalp',    # 0xf2
'syalh',    # 0xf3
'syam',    # 0xf4
'syab',    # 0xf5
'syabs',    # 0xf6
'syas',    # 0xf7
'syass',    # 0xf8
'syang',    # 0xf9
'syaj',    # 0xfa
'syac',    # 0xfb
'syak',    # 0xfc
'syat',    # 0xfd
'syap',    # 0xfe
'syah',    # 0xff
)
