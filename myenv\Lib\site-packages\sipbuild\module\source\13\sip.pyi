# Copyright (c) 2022, Riverbank Computing Limited
# All rights reserved.
#
# This copy of SIP is licensed for use under the terms of the SIP License
# Agreement.  See the file LICENSE for more details.
#
# This copy of SIP may also used under the terms of the GNU General Public
# License v2 or v3 as published by the Free Software Foundation which can be
# found in the files LICENSE-GPL2 and LICENSE-GPL3 included in this package.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTAB<PERSON>ITY AND FITNESS FOR A PARTICULAR PURPOSE
# ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
# LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
# CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
# <PERSON>U<PERSON><PERSON>TU<PERSON> GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
# INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
# CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
# ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
# POSSIBILITY OF SUCH DAMAGE.


from typing import Any, Generic, Iterable, overload, Sequence, TypeVar, Union


# PEP 484 has no explicit support for the buffer protocol so we just name types
# we know that implement it.
Buffer = Union[bytes, bytearray, memoryview, 'array', 'voidptr']


# Constants.
SIP_VERSION = ...       # type: int
SIP_VERSION_STR = ...   # type: str


# The bases for SIP generated types.
class wrappertype:
    def __init__(self, *args, **kwargs) -> None: ...

class simplewrapper:
    def __init__(self, *args, **kwargs) -> None: ...

class wrapper(simplewrapper): ...


# The array type.
_T = TypeVar('_T')

class array(Sequence[_T], Generic[_T]):

    @overload
    def __getitem__(self, key: int) -> _T: ...
    @overload
    def __getitem__(self, key: slice) -> 'array[_T]': ...

    @overload
    def __setitem__(self, key: int, value: _T) -> None: ...
    @overload
    def __setitem__(self, key: slice, value: Iterable[_T]) -> None: ...

    @overload
    def __delitem__(self, key: int) -> None: ...
    @overload
    def __delitem__(self, key: slice) -> None: ...

    def __len__(self) -> int: ...


# The voidptr type.
class voidptr:

    def __init__(self, addr: Union[int, Buffer], size: int = -1, writeable: bool = True) -> None: ...

    def __int__(self) -> int: ...

    @overload
    def __getitem__(self, i: int) -> bytes: ...

    @overload
    def __getitem__(self, s: slice) -> 'voidptr': ...

    def __len__(self) -> int: ...

    def __setitem__(self, i: Union[int, slice], v: Buffer) -> None: ...

    def asarray(self, size: int = -1) -> array[int]: ...

    # Python doesn't expose the capsule type.
    def ascapsule(self) -> Any: ...

    def asstring(self, size: int = -1) -> bytes: ...

    def getsize(self) -> int: ...

    def getwriteable(self) -> bool: ...

    def setsize(self, size: int) -> None: ...

    def setwriteable(self, writeable: bool) -> None: ...


# Remaining functions.
def assign(obj: simplewrapper, other: simplewrapper) -> None: ...
def cast(obj: simplewrapper, type: wrappertype) -> simplewrapper: ...
def delete(obj: simplewrapper) -> None: ...
def dump(obj: simplewrapper) -> None: ...
def enableautoconversion(type: wrappertype, enable: bool) -> bool: ...
def isdeleted(obj: simplewrapper) -> bool: ...
def ispycreated(obj: simplewrapper) -> bool: ...
def ispyowned(obj: simplewrapper) -> bool: ...
def setdeleted(obj: simplewrapper) -> None: ...
def settracemask(mask: int) -> None: ...
def transferback(obj: wrapper) -> None: ...
def transferto(obj: wrapper, owner: wrapper) -> None: ...
def unwrapinstance(obj: simplewrapper) -> None: ...
def wrapinstance(addr: int, type: wrappertype) -> simplewrapper: ...
