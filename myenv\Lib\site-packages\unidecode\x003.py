data = (
'',    # 0x00
'',    # 0x01
'',    # 0x02
'',    # 0x03
'',    # 0x04
'',    # 0x05
'',    # 0x06
'',    # 0x07
'',    # 0x08
'',    # 0x09
'',    # 0x0a
'',    # 0x0b
'',    # 0x0c
'',    # 0x0d
'',    # 0x0e
'',    # 0x0f
'',    # 0x10
'',    # 0x11
'',    # 0x12
'',    # 0x13
'',    # 0x14
'',    # 0x15
'',    # 0x16
'',    # 0x17
'',    # 0x18
'',    # 0x19
'',    # 0x1a
'',    # 0x1b
'',    # 0x1c
'',    # 0x1d
'',    # 0x1e
'',    # 0x1f
'',    # 0x20
'',    # 0x21
'',    # 0x22
'',    # 0x23
'',    # 0x24
'',    # 0x25
'',    # 0x26
'',    # 0x27
'',    # 0x28
'',    # 0x29
'',    # 0x2a
'',    # 0x2b
'',    # 0x2c
'',    # 0x2d
'',    # 0x2e
'',    # 0x2f
'',    # 0x30
'',    # 0x31
'',    # 0x32
'',    # 0x33
'',    # 0x34
'',    # 0x35
'',    # 0x36
'',    # 0x37
'',    # 0x38
'',    # 0x39
'',    # 0x3a
'',    # 0x3b
'',    # 0x3c
'',    # 0x3d
'',    # 0x3e
'',    # 0x3f
'',    # 0x40
'',    # 0x41
'',    # 0x42
'',    # 0x43
'',    # 0x44
'',    # 0x45
'',    # 0x46
'',    # 0x47
'',    # 0x48
'',    # 0x49
'',    # 0x4a
'',    # 0x4b
'',    # 0x4c
'',    # 0x4d
'',    # 0x4e
None,    # 0x4f
None,    # 0x50
None,    # 0x51
None,    # 0x52
None,    # 0x53
None,    # 0x54
None,    # 0x55
None,    # 0x56
None,    # 0x57
None,    # 0x58
None,    # 0x59
None,    # 0x5a
None,    # 0x5b
None,    # 0x5c
None,    # 0x5d
None,    # 0x5e
None,    # 0x5f
'',    # 0x60
'',    # 0x61
'',    # 0x62
'a',    # 0x63
'e',    # 0x64
'i',    # 0x65
'o',    # 0x66
'u',    # 0x67
'c',    # 0x68
'd',    # 0x69
'h',    # 0x6a
'm',    # 0x6b
'r',    # 0x6c
't',    # 0x6d
'v',    # 0x6e
'x',    # 0x6f
None,    # 0x70
None,    # 0x71
None,    # 0x72
None,    # 0x73
'\'',    # 0x74
',',    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
'',    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
'?',    # 0x7e
None,    # 0x7f
None,    # 0x80
None,    # 0x81
None,    # 0x82
None,    # 0x83
'',    # 0x84
'',    # 0x85
'A',    # 0x86
';',    # 0x87
'E',    # 0x88
'E',    # 0x89
'I',    # 0x8a
None,    # 0x8b
'O',    # 0x8c
None,    # 0x8d
'U',    # 0x8e
'O',    # 0x8f
'I',    # 0x90
'A',    # 0x91
'B',    # 0x92
'G',    # 0x93
'D',    # 0x94
'E',    # 0x95
'Z',    # 0x96
'E',    # 0x97
'Th',    # 0x98
'I',    # 0x99
'K',    # 0x9a
'L',    # 0x9b
'M',    # 0x9c
'N',    # 0x9d
'Ks',    # 0x9e
'O',    # 0x9f
'P',    # 0xa0
'R',    # 0xa1
None,    # 0xa2
'S',    # 0xa3
'T',    # 0xa4
'U',    # 0xa5
'Ph',    # 0xa6
'Kh',    # 0xa7
'Ps',    # 0xa8
'O',    # 0xa9
'I',    # 0xaa
'U',    # 0xab
'a',    # 0xac
'e',    # 0xad
'e',    # 0xae
'i',    # 0xaf
'u',    # 0xb0
'a',    # 0xb1
'b',    # 0xb2
'g',    # 0xb3
'd',    # 0xb4
'e',    # 0xb5
'z',    # 0xb6
'e',    # 0xb7
'th',    # 0xb8
'i',    # 0xb9
'k',    # 0xba
'l',    # 0xbb
'm',    # 0xbc
'n',    # 0xbd
'x',    # 0xbe
'o',    # 0xbf
'p',    # 0xc0
'r',    # 0xc1
's',    # 0xc2
's',    # 0xc3
't',    # 0xc4
'u',    # 0xc5
'ph',    # 0xc6
'kh',    # 0xc7
'ps',    # 0xc8
'o',    # 0xc9
'i',    # 0xca
'u',    # 0xcb
'o',    # 0xcc
'u',    # 0xcd
'o',    # 0xce
None,    # 0xcf
'b',    # 0xd0
'th',    # 0xd1
'U',    # 0xd2
'U',    # 0xd3
'U',    # 0xd4
'ph',    # 0xd5
'p',    # 0xd6
'&',    # 0xd7
None,    # 0xd8
None,    # 0xd9
'St',    # 0xda
'st',    # 0xdb
'W',    # 0xdc
'w',    # 0xdd
'Q',    # 0xde
'q',    # 0xdf
'Sp',    # 0xe0
'sp',    # 0xe1
'Sh',    # 0xe2
'sh',    # 0xe3
'F',    # 0xe4
'f',    # 0xe5
'Kh',    # 0xe6
'kh',    # 0xe7
'H',    # 0xe8
'h',    # 0xe9
'G',    # 0xea
'g',    # 0xeb
'CH',    # 0xec
'ch',    # 0xed
'Ti',    # 0xee
'ti',    # 0xef
'k',    # 0xf0
'r',    # 0xf1
'c',    # 0xf2
'j',    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
