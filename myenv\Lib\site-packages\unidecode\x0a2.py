data = (
'kax',    # 0x00
'ka',    # 0x01
'kap',    # 0x02
'kuox',    # 0x03
'kuo',    # 0x04
'kuop',    # 0x05
'kot',    # 0x06
'kox',    # 0x07
'ko',    # 0x08
'kop',    # 0x09
'ket',    # 0x0a
'kex',    # 0x0b
'ke',    # 0x0c
'kep',    # 0x0d
'kut',    # 0x0e
'kux',    # 0x0f
'ku',    # 0x10
'kup',    # 0x11
'kurx',    # 0x12
'kur',    # 0x13
'ggit',    # 0x14
'ggix',    # 0x15
'ggi',    # 0x16
'ggiex',    # 0x17
'ggie',    # 0x18
'ggiep',    # 0x19
'ggat',    # 0x1a
'ggax',    # 0x1b
'gga',    # 0x1c
'ggap',    # 0x1d
'gguot',    # 0x1e
'gguox',    # 0x1f
'gguo',    # 0x20
'gguop',    # 0x21
'ggot',    # 0x22
'ggox',    # 0x23
'ggo',    # 0x24
'ggop',    # 0x25
'gget',    # 0x26
'ggex',    # 0x27
'gge',    # 0x28
'ggep',    # 0x29
'ggut',    # 0x2a
'ggux',    # 0x2b
'ggu',    # 0x2c
'ggup',    # 0x2d
'ggurx',    # 0x2e
'ggur',    # 0x2f
'mgiex',    # 0x30
'mgie',    # 0x31
'mgat',    # 0x32
'mgax',    # 0x33
'mga',    # 0x34
'mgap',    # 0x35
'mguox',    # 0x36
'mguo',    # 0x37
'mguop',    # 0x38
'mgot',    # 0x39
'mgox',    # 0x3a
'mgo',    # 0x3b
'mgop',    # 0x3c
'mgex',    # 0x3d
'mge',    # 0x3e
'mgep',    # 0x3f
'mgut',    # 0x40
'mgux',    # 0x41
'mgu',    # 0x42
'mgup',    # 0x43
'mgurx',    # 0x44
'mgur',    # 0x45
'hxit',    # 0x46
'hxix',    # 0x47
'hxi',    # 0x48
'hxip',    # 0x49
'hxiet',    # 0x4a
'hxiex',    # 0x4b
'hxie',    # 0x4c
'hxiep',    # 0x4d
'hxat',    # 0x4e
'hxax',    # 0x4f
'hxa',    # 0x50
'hxap',    # 0x51
'hxuot',    # 0x52
'hxuox',    # 0x53
'hxuo',    # 0x54
'hxuop',    # 0x55
'hxot',    # 0x56
'hxox',    # 0x57
'hxo',    # 0x58
'hxop',    # 0x59
'hxex',    # 0x5a
'hxe',    # 0x5b
'hxep',    # 0x5c
'ngiex',    # 0x5d
'ngie',    # 0x5e
'ngiep',    # 0x5f
'ngat',    # 0x60
'ngax',    # 0x61
'nga',    # 0x62
'ngap',    # 0x63
'nguot',    # 0x64
'nguox',    # 0x65
'nguo',    # 0x66
'ngot',    # 0x67
'ngox',    # 0x68
'ngo',    # 0x69
'ngop',    # 0x6a
'ngex',    # 0x6b
'nge',    # 0x6c
'ngep',    # 0x6d
'hit',    # 0x6e
'hiex',    # 0x6f
'hie',    # 0x70
'hat',    # 0x71
'hax',    # 0x72
'ha',    # 0x73
'hap',    # 0x74
'huot',    # 0x75
'huox',    # 0x76
'huo',    # 0x77
'huop',    # 0x78
'hot',    # 0x79
'hox',    # 0x7a
'ho',    # 0x7b
'hop',    # 0x7c
'hex',    # 0x7d
'he',    # 0x7e
'hep',    # 0x7f
'wat',    # 0x80
'wax',    # 0x81
'wa',    # 0x82
'wap',    # 0x83
'wuox',    # 0x84
'wuo',    # 0x85
'wuop',    # 0x86
'wox',    # 0x87
'wo',    # 0x88
'wop',    # 0x89
'wex',    # 0x8a
'we',    # 0x8b
'wep',    # 0x8c
'zit',    # 0x8d
'zix',    # 0x8e
'zi',    # 0x8f
'zip',    # 0x90
'ziex',    # 0x91
'zie',    # 0x92
'ziep',    # 0x93
'zat',    # 0x94
'zax',    # 0x95
'za',    # 0x96
'zap',    # 0x97
'zuox',    # 0x98
'zuo',    # 0x99
'zuop',    # 0x9a
'zot',    # 0x9b
'zox',    # 0x9c
'zo',    # 0x9d
'zop',    # 0x9e
'zex',    # 0x9f
'ze',    # 0xa0
'zep',    # 0xa1
'zut',    # 0xa2
'zux',    # 0xa3
'zu',    # 0xa4
'zup',    # 0xa5
'zurx',    # 0xa6
'zur',    # 0xa7
'zyt',    # 0xa8
'zyx',    # 0xa9
'zy',    # 0xaa
'zyp',    # 0xab
'zyrx',    # 0xac
'zyr',    # 0xad
'cit',    # 0xae
'cix',    # 0xaf
'ci',    # 0xb0
'cip',    # 0xb1
'ciet',    # 0xb2
'ciex',    # 0xb3
'cie',    # 0xb4
'ciep',    # 0xb5
'cat',    # 0xb6
'cax',    # 0xb7
'ca',    # 0xb8
'cap',    # 0xb9
'cuox',    # 0xba
'cuo',    # 0xbb
'cuop',    # 0xbc
'cot',    # 0xbd
'cox',    # 0xbe
'co',    # 0xbf
'cop',    # 0xc0
'cex',    # 0xc1
'ce',    # 0xc2
'cep',    # 0xc3
'cut',    # 0xc4
'cux',    # 0xc5
'cu',    # 0xc6
'cup',    # 0xc7
'curx',    # 0xc8
'cur',    # 0xc9
'cyt',    # 0xca
'cyx',    # 0xcb
'cy',    # 0xcc
'cyp',    # 0xcd
'cyrx',    # 0xce
'cyr',    # 0xcf
'zzit',    # 0xd0
'zzix',    # 0xd1
'zzi',    # 0xd2
'zzip',    # 0xd3
'zziet',    # 0xd4
'zziex',    # 0xd5
'zzie',    # 0xd6
'zziep',    # 0xd7
'zzat',    # 0xd8
'zzax',    # 0xd9
'zza',    # 0xda
'zzap',    # 0xdb
'zzox',    # 0xdc
'zzo',    # 0xdd
'zzop',    # 0xde
'zzex',    # 0xdf
'zze',    # 0xe0
'zzep',    # 0xe1
'zzux',    # 0xe2
'zzu',    # 0xe3
'zzup',    # 0xe4
'zzurx',    # 0xe5
'zzur',    # 0xe6
'zzyt',    # 0xe7
'zzyx',    # 0xe8
'zzy',    # 0xe9
'zzyp',    # 0xea
'zzyrx',    # 0xeb
'zzyr',    # 0xec
'nzit',    # 0xed
'nzix',    # 0xee
'nzi',    # 0xef
'nzip',    # 0xf0
'nziex',    # 0xf1
'nzie',    # 0xf2
'nziep',    # 0xf3
'nzat',    # 0xf4
'nzax',    # 0xf5
'nza',    # 0xf6
'nzap',    # 0xf7
'nzuox',    # 0xf8
'nzuo',    # 0xf9
'nzox',    # 0xfa
'nzop',    # 0xfb
'nzex',    # 0xfc
'nze',    # 0xfd
'nzux',    # 0xfe
'nzu',    # 0xff
)
