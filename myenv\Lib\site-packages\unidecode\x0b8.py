data = (
'reoss',    # 0x00
'reong',    # 0x01
'reoj',    # 0x02
'reoc',    # 0x03
'reok',    # 0x04
'reot',    # 0x05
'reop',    # 0x06
'reoh',    # 0x07
're',    # 0x08
'reg',    # 0x09
'regg',    # 0x0a
'regs',    # 0x0b
'ren',    # 0x0c
'renj',    # 0x0d
'renh',    # 0x0e
'red',    # 0x0f
'rel',    # 0x10
'relg',    # 0x11
'relm',    # 0x12
'relb',    # 0x13
'rels',    # 0x14
'relt',    # 0x15
'relp',    # 0x16
'relh',    # 0x17
'rem',    # 0x18
'reb',    # 0x19
'rebs',    # 0x1a
'res',    # 0x1b
'ress',    # 0x1c
'reng',    # 0x1d
'rej',    # 0x1e
'rec',    # 0x1f
'rek',    # 0x20
'ret',    # 0x21
'rep',    # 0x22
'reh',    # 0x23
'ryeo',    # 0x24
'ryeog',    # 0x25
'ryeogg',    # 0x26
'ryeogs',    # 0x27
'ryeon',    # 0x28
'ryeonj',    # 0x29
'ryeonh',    # 0x2a
'ryeod',    # 0x2b
'ryeol',    # 0x2c
'ryeolg',    # 0x2d
'ryeolm',    # 0x2e
'ryeolb',    # 0x2f
'ryeols',    # 0x30
'ryeolt',    # 0x31
'ryeolp',    # 0x32
'ryeolh',    # 0x33
'ryeom',    # 0x34
'ryeob',    # 0x35
'ryeobs',    # 0x36
'ryeos',    # 0x37
'ryeoss',    # 0x38
'ryeong',    # 0x39
'ryeoj',    # 0x3a
'ryeoc',    # 0x3b
'ryeok',    # 0x3c
'ryeot',    # 0x3d
'ryeop',    # 0x3e
'ryeoh',    # 0x3f
'rye',    # 0x40
'ryeg',    # 0x41
'ryegg',    # 0x42
'ryegs',    # 0x43
'ryen',    # 0x44
'ryenj',    # 0x45
'ryenh',    # 0x46
'ryed',    # 0x47
'ryel',    # 0x48
'ryelg',    # 0x49
'ryelm',    # 0x4a
'ryelb',    # 0x4b
'ryels',    # 0x4c
'ryelt',    # 0x4d
'ryelp',    # 0x4e
'ryelh',    # 0x4f
'ryem',    # 0x50
'ryeb',    # 0x51
'ryebs',    # 0x52
'ryes',    # 0x53
'ryess',    # 0x54
'ryeng',    # 0x55
'ryej',    # 0x56
'ryec',    # 0x57
'ryek',    # 0x58
'ryet',    # 0x59
'ryep',    # 0x5a
'ryeh',    # 0x5b
'ro',    # 0x5c
'rog',    # 0x5d
'rogg',    # 0x5e
'rogs',    # 0x5f
'ron',    # 0x60
'ronj',    # 0x61
'ronh',    # 0x62
'rod',    # 0x63
'rol',    # 0x64
'rolg',    # 0x65
'rolm',    # 0x66
'rolb',    # 0x67
'rols',    # 0x68
'rolt',    # 0x69
'rolp',    # 0x6a
'rolh',    # 0x6b
'rom',    # 0x6c
'rob',    # 0x6d
'robs',    # 0x6e
'ros',    # 0x6f
'ross',    # 0x70
'rong',    # 0x71
'roj',    # 0x72
'roc',    # 0x73
'rok',    # 0x74
'rot',    # 0x75
'rop',    # 0x76
'roh',    # 0x77
'rwa',    # 0x78
'rwag',    # 0x79
'rwagg',    # 0x7a
'rwags',    # 0x7b
'rwan',    # 0x7c
'rwanj',    # 0x7d
'rwanh',    # 0x7e
'rwad',    # 0x7f
'rwal',    # 0x80
'rwalg',    # 0x81
'rwalm',    # 0x82
'rwalb',    # 0x83
'rwals',    # 0x84
'rwalt',    # 0x85
'rwalp',    # 0x86
'rwalh',    # 0x87
'rwam',    # 0x88
'rwab',    # 0x89
'rwabs',    # 0x8a
'rwas',    # 0x8b
'rwass',    # 0x8c
'rwang',    # 0x8d
'rwaj',    # 0x8e
'rwac',    # 0x8f
'rwak',    # 0x90
'rwat',    # 0x91
'rwap',    # 0x92
'rwah',    # 0x93
'rwae',    # 0x94
'rwaeg',    # 0x95
'rwaegg',    # 0x96
'rwaegs',    # 0x97
'rwaen',    # 0x98
'rwaenj',    # 0x99
'rwaenh',    # 0x9a
'rwaed',    # 0x9b
'rwael',    # 0x9c
'rwaelg',    # 0x9d
'rwaelm',    # 0x9e
'rwaelb',    # 0x9f
'rwaels',    # 0xa0
'rwaelt',    # 0xa1
'rwaelp',    # 0xa2
'rwaelh',    # 0xa3
'rwaem',    # 0xa4
'rwaeb',    # 0xa5
'rwaebs',    # 0xa6
'rwaes',    # 0xa7
'rwaess',    # 0xa8
'rwaeng',    # 0xa9
'rwaej',    # 0xaa
'rwaec',    # 0xab
'rwaek',    # 0xac
'rwaet',    # 0xad
'rwaep',    # 0xae
'rwaeh',    # 0xaf
'roe',    # 0xb0
'roeg',    # 0xb1
'roegg',    # 0xb2
'roegs',    # 0xb3
'roen',    # 0xb4
'roenj',    # 0xb5
'roenh',    # 0xb6
'roed',    # 0xb7
'roel',    # 0xb8
'roelg',    # 0xb9
'roelm',    # 0xba
'roelb',    # 0xbb
'roels',    # 0xbc
'roelt',    # 0xbd
'roelp',    # 0xbe
'roelh',    # 0xbf
'roem',    # 0xc0
'roeb',    # 0xc1
'roebs',    # 0xc2
'roes',    # 0xc3
'roess',    # 0xc4
'roeng',    # 0xc5
'roej',    # 0xc6
'roec',    # 0xc7
'roek',    # 0xc8
'roet',    # 0xc9
'roep',    # 0xca
'roeh',    # 0xcb
'ryo',    # 0xcc
'ryog',    # 0xcd
'ryogg',    # 0xce
'ryogs',    # 0xcf
'ryon',    # 0xd0
'ryonj',    # 0xd1
'ryonh',    # 0xd2
'ryod',    # 0xd3
'ryol',    # 0xd4
'ryolg',    # 0xd5
'ryolm',    # 0xd6
'ryolb',    # 0xd7
'ryols',    # 0xd8
'ryolt',    # 0xd9
'ryolp',    # 0xda
'ryolh',    # 0xdb
'ryom',    # 0xdc
'ryob',    # 0xdd
'ryobs',    # 0xde
'ryos',    # 0xdf
'ryoss',    # 0xe0
'ryong',    # 0xe1
'ryoj',    # 0xe2
'ryoc',    # 0xe3
'ryok',    # 0xe4
'ryot',    # 0xe5
'ryop',    # 0xe6
'ryoh',    # 0xe7
'ru',    # 0xe8
'rug',    # 0xe9
'rugg',    # 0xea
'rugs',    # 0xeb
'run',    # 0xec
'runj',    # 0xed
'runh',    # 0xee
'rud',    # 0xef
'rul',    # 0xf0
'rulg',    # 0xf1
'rulm',    # 0xf2
'rulb',    # 0xf3
'ruls',    # 0xf4
'rult',    # 0xf5
'rulp',    # 0xf6
'rulh',    # 0xf7
'rum',    # 0xf8
'rub',    # 0xf9
'rubs',    # 0xfa
'rus',    # 0xfb
'russ',    # 0xfc
'rung',    # 0xfd
'ruj',    # 0xfe
'ruc',    # 0xff
)
