data = (
'it',    # 0x00
'ix',    # 0x01
'i',    # 0x02
'ip',    # 0x03
'iet',    # 0x04
'iex',    # 0x05
'ie',    # 0x06
'iep',    # 0x07
'at',    # 0x08
'ax',    # 0x09
'a',    # 0x0a
'ap',    # 0x0b
'uox',    # 0x0c
'uo',    # 0x0d
'uop',    # 0x0e
'ot',    # 0x0f
'ox',    # 0x10
'o',    # 0x11
'op',    # 0x12
'ex',    # 0x13
'e',    # 0x14
'wu',    # 0x15
'bit',    # 0x16
'bix',    # 0x17
'bi',    # 0x18
'bip',    # 0x19
'biet',    # 0x1a
'biex',    # 0x1b
'bie',    # 0x1c
'biep',    # 0x1d
'bat',    # 0x1e
'bax',    # 0x1f
'ba',    # 0x20
'bap',    # 0x21
'buox',    # 0x22
'buo',    # 0x23
'buop',    # 0x24
'bot',    # 0x25
'box',    # 0x26
'bo',    # 0x27
'bop',    # 0x28
'bex',    # 0x29
'be',    # 0x2a
'bep',    # 0x2b
'but',    # 0x2c
'bux',    # 0x2d
'bu',    # 0x2e
'bup',    # 0x2f
'burx',    # 0x30
'bur',    # 0x31
'byt',    # 0x32
'byx',    # 0x33
'by',    # 0x34
'byp',    # 0x35
'byrx',    # 0x36
'byr',    # 0x37
'pit',    # 0x38
'pix',    # 0x39
'pi',    # 0x3a
'pip',    # 0x3b
'piex',    # 0x3c
'pie',    # 0x3d
'piep',    # 0x3e
'pat',    # 0x3f
'pax',    # 0x40
'pa',    # 0x41
'pap',    # 0x42
'puox',    # 0x43
'puo',    # 0x44
'puop',    # 0x45
'pot',    # 0x46
'pox',    # 0x47
'po',    # 0x48
'pop',    # 0x49
'put',    # 0x4a
'pux',    # 0x4b
'pu',    # 0x4c
'pup',    # 0x4d
'purx',    # 0x4e
'pur',    # 0x4f
'pyt',    # 0x50
'pyx',    # 0x51
'py',    # 0x52
'pyp',    # 0x53
'pyrx',    # 0x54
'pyr',    # 0x55
'bbit',    # 0x56
'bbix',    # 0x57
'bbi',    # 0x58
'bbip',    # 0x59
'bbiet',    # 0x5a
'bbiex',    # 0x5b
'bbie',    # 0x5c
'bbiep',    # 0x5d
'bbat',    # 0x5e
'bbax',    # 0x5f
'bba',    # 0x60
'bbap',    # 0x61
'bbuox',    # 0x62
'bbuo',    # 0x63
'bbuop',    # 0x64
'bbot',    # 0x65
'bbox',    # 0x66
'bbo',    # 0x67
'bbop',    # 0x68
'bbex',    # 0x69
'bbe',    # 0x6a
'bbep',    # 0x6b
'bbut',    # 0x6c
'bbux',    # 0x6d
'bbu',    # 0x6e
'bbup',    # 0x6f
'bburx',    # 0x70
'bbur',    # 0x71
'bbyt',    # 0x72
'bbyx',    # 0x73
'bby',    # 0x74
'bbyp',    # 0x75
'nbit',    # 0x76
'nbix',    # 0x77
'nbi',    # 0x78
'nbip',    # 0x79
'nbiex',    # 0x7a
'nbie',    # 0x7b
'nbiep',    # 0x7c
'nbat',    # 0x7d
'nbax',    # 0x7e
'nba',    # 0x7f
'nbap',    # 0x80
'nbot',    # 0x81
'nbox',    # 0x82
'nbo',    # 0x83
'nbop',    # 0x84
'nbut',    # 0x85
'nbux',    # 0x86
'nbu',    # 0x87
'nbup',    # 0x88
'nburx',    # 0x89
'nbur',    # 0x8a
'nbyt',    # 0x8b
'nbyx',    # 0x8c
'nby',    # 0x8d
'nbyp',    # 0x8e
'nbyrx',    # 0x8f
'nbyr',    # 0x90
'hmit',    # 0x91
'hmix',    # 0x92
'hmi',    # 0x93
'hmip',    # 0x94
'hmiex',    # 0x95
'hmie',    # 0x96
'hmiep',    # 0x97
'hmat',    # 0x98
'hmax',    # 0x99
'hma',    # 0x9a
'hmap',    # 0x9b
'hmuox',    # 0x9c
'hmuo',    # 0x9d
'hmuop',    # 0x9e
'hmot',    # 0x9f
'hmox',    # 0xa0
'hmo',    # 0xa1
'hmop',    # 0xa2
'hmut',    # 0xa3
'hmux',    # 0xa4
'hmu',    # 0xa5
'hmup',    # 0xa6
'hmurx',    # 0xa7
'hmur',    # 0xa8
'hmyx',    # 0xa9
'hmy',    # 0xaa
'hmyp',    # 0xab
'hmyrx',    # 0xac
'hmyr',    # 0xad
'mit',    # 0xae
'mix',    # 0xaf
'mi',    # 0xb0
'mip',    # 0xb1
'miex',    # 0xb2
'mie',    # 0xb3
'miep',    # 0xb4
'mat',    # 0xb5
'max',    # 0xb6
'ma',    # 0xb7
'map',    # 0xb8
'muot',    # 0xb9
'muox',    # 0xba
'muo',    # 0xbb
'muop',    # 0xbc
'mot',    # 0xbd
'mox',    # 0xbe
'mo',    # 0xbf
'mop',    # 0xc0
'mex',    # 0xc1
'me',    # 0xc2
'mut',    # 0xc3
'mux',    # 0xc4
'mu',    # 0xc5
'mup',    # 0xc6
'murx',    # 0xc7
'mur',    # 0xc8
'myt',    # 0xc9
'myx',    # 0xca
'my',    # 0xcb
'myp',    # 0xcc
'fit',    # 0xcd
'fix',    # 0xce
'fi',    # 0xcf
'fip',    # 0xd0
'fat',    # 0xd1
'fax',    # 0xd2
'fa',    # 0xd3
'fap',    # 0xd4
'fox',    # 0xd5
'fo',    # 0xd6
'fop',    # 0xd7
'fut',    # 0xd8
'fux',    # 0xd9
'fu',    # 0xda
'fup',    # 0xdb
'furx',    # 0xdc
'fur',    # 0xdd
'fyt',    # 0xde
'fyx',    # 0xdf
'fy',    # 0xe0
'fyp',    # 0xe1
'vit',    # 0xe2
'vix',    # 0xe3
'vi',    # 0xe4
'vip',    # 0xe5
'viet',    # 0xe6
'viex',    # 0xe7
'vie',    # 0xe8
'viep',    # 0xe9
'vat',    # 0xea
'vax',    # 0xeb
'va',    # 0xec
'vap',    # 0xed
'vot',    # 0xee
'vox',    # 0xef
'vo',    # 0xf0
'vop',    # 0xf1
'vex',    # 0xf2
'vep',    # 0xf3
'vut',    # 0xf4
'vux',    # 0xf5
'vu',    # 0xf6
'vup',    # 0xf7
'vurx',    # 0xf8
'vur',    # 0xf9
'vyt',    # 0xfa
'vyx',    # 0xfb
'vy',    # 0xfc
'vyp',    # 0xfd
'vyrx',    # 0xfe
'vyr',    # 0xff
)
