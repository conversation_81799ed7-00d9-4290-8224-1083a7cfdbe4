data = (
None,    # 0x00
None,    # 0x01
None,    # 0x02
None,    # 0x03
None,    # 0x04
'B',    # 0x05
'P',    # 0x06
'M',    # 0x07
'F',    # 0x08
'D',    # 0x09
'T',    # 0x0a
'N',    # 0x0b
'L',    # 0x0c
'G',    # 0x0d
'K',    # 0x0e
'H',    # 0x0f
'J',    # 0x10
'Q',    # 0x11
'X',    # 0x12
'ZH',    # 0x13
'CH',    # 0x14
'SH',    # 0x15
'R',    # 0x16
'Z',    # 0x17
'C',    # 0x18
'S',    # 0x19
'A',    # 0x1a
'O',    # 0x1b
'E',    # 0x1c
'EH',    # 0x1d
'AI',    # 0x1e
'EI',    # 0x1f
'AU',    # 0x20
'OU',    # 0x21
'AN',    # 0x22
'EN',    # 0x23
'ANG',    # 0x24
'ENG',    # 0x25
'ER',    # 0x26
'I',    # 0x27
'U',    # 0x28
'IU',    # 0x29
'V',    # 0x2a
'NG',    # 0x2b
'GN',    # 0x2c
None,    # 0x2d
None,    # 0x2e
None,    # 0x2f
None,    # 0x30
'g',    # 0x31
'gg',    # 0x32
'gs',    # 0x33
'n',    # 0x34
'nj',    # 0x35
'nh',    # 0x36
'd',    # 0x37
'dd',    # 0x38
'r',    # 0x39
'lg',    # 0x3a
'lm',    # 0x3b
'lb',    # 0x3c
'ls',    # 0x3d
'lt',    # 0x3e
'lp',    # 0x3f
'rh',    # 0x40
'm',    # 0x41
'b',    # 0x42
'bb',    # 0x43
'bs',    # 0x44
's',    # 0x45
'ss',    # 0x46
'',    # 0x47
'j',    # 0x48
'jj',    # 0x49
'c',    # 0x4a
'k',    # 0x4b
't',    # 0x4c
'p',    # 0x4d
'h',    # 0x4e
'a',    # 0x4f
'ae',    # 0x50
'ya',    # 0x51
'yae',    # 0x52
'eo',    # 0x53
'e',    # 0x54
'yeo',    # 0x55
'ye',    # 0x56
'o',    # 0x57
'wa',    # 0x58
'wae',    # 0x59
'oe',    # 0x5a
'yo',    # 0x5b
'u',    # 0x5c
'weo',    # 0x5d
'we',    # 0x5e
'wi',    # 0x5f
'yu',    # 0x60
'eu',    # 0x61
'yi',    # 0x62
'i',    # 0x63
'',    # 0x64
'nn',    # 0x65
'nd',    # 0x66
'ns',    # 0x67
'nZ',    # 0x68
'lgs',    # 0x69
'ld',    # 0x6a
'lbs',    # 0x6b
'lZ',    # 0x6c
'lQ',    # 0x6d
'mb',    # 0x6e
'ms',    # 0x6f
'mZ',    # 0x70
'mN',    # 0x71
'bg',    # 0x72
'',    # 0x73
'bsg',    # 0x74
'bst',    # 0x75
'bj',    # 0x76
'bt',    # 0x77
'bN',    # 0x78
'bbN',    # 0x79
'sg',    # 0x7a
'sn',    # 0x7b
'sd',    # 0x7c
'sb',    # 0x7d
'sj',    # 0x7e
'Z',    # 0x7f
'',    # 0x80
'N',    # 0x81
'Ns',    # 0x82
'NZ',    # 0x83
'pN',    # 0x84
'hh',    # 0x85
'Q',    # 0x86
'yo-ya',    # 0x87
'yo-yae',    # 0x88
'yo-i',    # 0x89
'yu-yeo',    # 0x8a
'yu-ye',    # 0x8b
'yu-i',    # 0x8c
'U',    # 0x8d
'U-i',    # 0x8e
None,    # 0x8f
'',    # 0x90
'',    # 0x91
'',    # 0x92
'',    # 0x93
'',    # 0x94
'',    # 0x95
'',    # 0x96
'',    # 0x97
'',    # 0x98
'',    # 0x99
'',    # 0x9a
'',    # 0x9b
'',    # 0x9c
'',    # 0x9d
'',    # 0x9e
'',    # 0x9f
'BU',    # 0xa0
'ZI',    # 0xa1
'JI',    # 0xa2
'GU',    # 0xa3
'EE',    # 0xa4
'ENN',    # 0xa5
'OO',    # 0xa6
'ONN',    # 0xa7
'IR',    # 0xa8
'ANN',    # 0xa9
'INN',    # 0xaa
'UNN',    # 0xab
'IM',    # 0xac
'NGG',    # 0xad
'AINN',    # 0xae
'AUNN',    # 0xaf
'AM',    # 0xb0
'OM',    # 0xb1
'ONG',    # 0xb2
'INNN',    # 0xb3
'P',    # 0xb4
'T',    # 0xb5
'K',    # 0xb6
'H',    # 0xb7
None,    # 0xb8
None,    # 0xb9
None,    # 0xba
None,    # 0xbb
None,    # 0xbc
None,    # 0xbd
None,    # 0xbe
None,    # 0xbf
None,    # 0xc0
None,    # 0xc1
None,    # 0xc2
None,    # 0xc3
None,    # 0xc4
None,    # 0xc5
None,    # 0xc6
None,    # 0xc7
None,    # 0xc8
None,    # 0xc9
None,    # 0xca
None,    # 0xcb
None,    # 0xcc
None,    # 0xcd
None,    # 0xce
None,    # 0xcf
None,    # 0xd0
None,    # 0xd1
None,    # 0xd2
None,    # 0xd3
None,    # 0xd4
None,    # 0xd5
None,    # 0xd6
None,    # 0xd7
None,    # 0xd8
None,    # 0xd9
None,    # 0xda
None,    # 0xdb
None,    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
None,    # 0xe6
None,    # 0xe7
None,    # 0xe8
None,    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
