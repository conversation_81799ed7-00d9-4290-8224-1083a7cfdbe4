data = (
None,    # 0x00
None,    # 0x01
None,    # 0x02
None,    # 0x03
None,    # 0x04
None,    # 0x05
None,    # 0x06
None,    # 0x07
None,    # 0x08
None,    # 0x09
None,    # 0x0a
None,    # 0x0b
',',    # 0x0c
None,    # 0x0d
None,    # 0x0e
None,    # 0x0f
None,    # 0x10
None,    # 0x11
None,    # 0x12
None,    # 0x13
None,    # 0x14
None,    # 0x15
None,    # 0x16
None,    # 0x17
None,    # 0x18
None,    # 0x19
None,    # 0x1a
';',    # 0x1b
None,    # 0x1c
None,    # 0x1d
None,    # 0x1e
'?',    # 0x1f
None,    # 0x20
'',    # 0x21
'a',    # 0x22
'\'',    # 0x23
'w\'',    # 0x24
'',    # 0x25
'y\'',    # 0x26
'',    # 0x27
'b',    # 0x28
'@',    # 0x29
't',    # 0x2a
'th',    # 0x2b
'j',    # 0x2c
'H',    # 0x2d
'kh',    # 0x2e
'd',    # 0x2f
'dh',    # 0x30
'r',    # 0x31
'z',    # 0x32
's',    # 0x33
'sh',    # 0x34
'S',    # 0x35
'D',    # 0x36
'T',    # 0x37
'Z',    # 0x38
'`',    # 0x39
'G',    # 0x3a
None,    # 0x3b
None,    # 0x3c
None,    # 0x3d
None,    # 0x3e
None,    # 0x3f
'',    # 0x40
'f',    # 0x41
'q',    # 0x42
'k',    # 0x43
'l',    # 0x44
'm',    # 0x45
'n',    # 0x46
'h',    # 0x47
'w',    # 0x48
'~',    # 0x49
'y',    # 0x4a
'an',    # 0x4b
'un',    # 0x4c
'in',    # 0x4d
'a',    # 0x4e
'u',    # 0x4f
'i',    # 0x50
'W',    # 0x51
'',    # 0x52
'',    # 0x53
'\'',    # 0x54
'\'',    # 0x55
None,    # 0x56
None,    # 0x57
None,    # 0x58
None,    # 0x59
None,    # 0x5a
None,    # 0x5b
None,    # 0x5c
None,    # 0x5d
None,    # 0x5e
None,    # 0x5f
'0',    # 0x60
'1',    # 0x61
'2',    # 0x62
'3',    # 0x63
'4',    # 0x64
'5',    # 0x65
'6',    # 0x66
'7',    # 0x67
'8',    # 0x68
'9',    # 0x69
'%',    # 0x6a
'.',    # 0x6b
',',    # 0x6c
'*',    # 0x6d
None,    # 0x6e
None,    # 0x6f
'',    # 0x70
'\'',    # 0x71
'\'',    # 0x72
'\'',    # 0x73
'',    # 0x74
'\'',    # 0x75
'\'w',    # 0x76
'\'u',    # 0x77
'\'y',    # 0x78
'tt',    # 0x79
'tth',    # 0x7a
'b',    # 0x7b
't',    # 0x7c
'T',    # 0x7d
'p',    # 0x7e
'th',    # 0x7f
'bh',    # 0x80
'\'h',    # 0x81
'H',    # 0x82
'ny',    # 0x83
'dy',    # 0x84
'H',    # 0x85
'ch',    # 0x86
'cch',    # 0x87
'dd',    # 0x88
'D',    # 0x89
'D',    # 0x8a
'Dt',    # 0x8b
'dh',    # 0x8c
'ddh',    # 0x8d
'd',    # 0x8e
'D',    # 0x8f
'D',    # 0x90
'rr',    # 0x91
'R',    # 0x92
'R',    # 0x93
'R',    # 0x94
'R',    # 0x95
'R',    # 0x96
'R',    # 0x97
'j',    # 0x98
'R',    # 0x99
'S',    # 0x9a
'S',    # 0x9b
'S',    # 0x9c
'S',    # 0x9d
'S',    # 0x9e
'T',    # 0x9f
'GH',    # 0xa0
'F',    # 0xa1
'F',    # 0xa2
'F',    # 0xa3
'v',    # 0xa4
'f',    # 0xa5
'ph',    # 0xa6
'Q',    # 0xa7
'Q',    # 0xa8
'kh',    # 0xa9
'k',    # 0xaa
'K',    # 0xab
'K',    # 0xac
'ng',    # 0xad
'K',    # 0xae
'g',    # 0xaf
'G',    # 0xb0
'N',    # 0xb1
'G',    # 0xb2
'G',    # 0xb3
'G',    # 0xb4
'L',    # 0xb5
'L',    # 0xb6
'L',    # 0xb7
'L',    # 0xb8
'N',    # 0xb9
'N',    # 0xba
'N',    # 0xbb
'N',    # 0xbc
'N',    # 0xbd
'h',    # 0xbe
'Ch',    # 0xbf
'hy',    # 0xc0
'h',    # 0xc1
'H',    # 0xc2
'@',    # 0xc3
'W',    # 0xc4
'oe',    # 0xc5
'oe',    # 0xc6
'u',    # 0xc7
'yu',    # 0xc8
'yu',    # 0xc9
'W',    # 0xca
'v',    # 0xcb
'y',    # 0xcc
'Y',    # 0xcd
'Y',    # 0xce
'W',    # 0xcf
'',    # 0xd0
'',    # 0xd1
'y',    # 0xd2
'y\'',    # 0xd3
'.',    # 0xd4
'ae',    # 0xd5
'',    # 0xd6
'',    # 0xd7
'',    # 0xd8
'',    # 0xd9
'',    # 0xda
'',    # 0xdb
'',    # 0xdc
'@',    # 0xdd
'#',    # 0xde
'',    # 0xdf
'',    # 0xe0
'',    # 0xe1
'',    # 0xe2
'',    # 0xe3
'',    # 0xe4
'',    # 0xe5
'',    # 0xe6
'',    # 0xe7
'',    # 0xe8
'^',    # 0xe9
'',    # 0xea
'',    # 0xeb
'',    # 0xec
'',    # 0xed
None,    # 0xee
None,    # 0xef
'0',    # 0xf0
'1',    # 0xf1
'2',    # 0xf2
'3',    # 0xf3
'4',    # 0xf4
'5',    # 0xf5
'6',    # 0xf6
'7',    # 0xf7
'8',    # 0xf8
'9',    # 0xf9
'Sh',    # 0xfa
'D',    # 0xfb
'Gh',    # 0xfc
'&',    # 0xfd
'+m',    # 0xfe
)
