data = (
'moen',    # 0x00
'moenj',    # 0x01
'moenh',    # 0x02
'moed',    # 0x03
'moel',    # 0x04
'moelg',    # 0x05
'moelm',    # 0x06
'moelb',    # 0x07
'moels',    # 0x08
'moelt',    # 0x09
'moelp',    # 0x0a
'moelh',    # 0x0b
'moem',    # 0x0c
'moeb',    # 0x0d
'moebs',    # 0x0e
'moes',    # 0x0f
'moess',    # 0x10
'moeng',    # 0x11
'moej',    # 0x12
'moec',    # 0x13
'moek',    # 0x14
'moet',    # 0x15
'moep',    # 0x16
'moeh',    # 0x17
'myo',    # 0x18
'myog',    # 0x19
'myogg',    # 0x1a
'myogs',    # 0x1b
'myon',    # 0x1c
'myonj',    # 0x1d
'myonh',    # 0x1e
'myod',    # 0x1f
'myol',    # 0x20
'myolg',    # 0x21
'myolm',    # 0x22
'myolb',    # 0x23
'myols',    # 0x24
'myolt',    # 0x25
'myolp',    # 0x26
'myolh',    # 0x27
'myom',    # 0x28
'myob',    # 0x29
'myobs',    # 0x2a
'myos',    # 0x2b
'myoss',    # 0x2c
'myong',    # 0x2d
'myoj',    # 0x2e
'myoc',    # 0x2f
'myok',    # 0x30
'myot',    # 0x31
'myop',    # 0x32
'myoh',    # 0x33
'mu',    # 0x34
'mug',    # 0x35
'mugg',    # 0x36
'mugs',    # 0x37
'mun',    # 0x38
'munj',    # 0x39
'munh',    # 0x3a
'mud',    # 0x3b
'mul',    # 0x3c
'mulg',    # 0x3d
'mulm',    # 0x3e
'mulb',    # 0x3f
'muls',    # 0x40
'mult',    # 0x41
'mulp',    # 0x42
'mulh',    # 0x43
'mum',    # 0x44
'mub',    # 0x45
'mubs',    # 0x46
'mus',    # 0x47
'muss',    # 0x48
'mung',    # 0x49
'muj',    # 0x4a
'muc',    # 0x4b
'muk',    # 0x4c
'mut',    # 0x4d
'mup',    # 0x4e
'muh',    # 0x4f
'mweo',    # 0x50
'mweog',    # 0x51
'mweogg',    # 0x52
'mweogs',    # 0x53
'mweon',    # 0x54
'mweonj',    # 0x55
'mweonh',    # 0x56
'mweod',    # 0x57
'mweol',    # 0x58
'mweolg',    # 0x59
'mweolm',    # 0x5a
'mweolb',    # 0x5b
'mweols',    # 0x5c
'mweolt',    # 0x5d
'mweolp',    # 0x5e
'mweolh',    # 0x5f
'mweom',    # 0x60
'mweob',    # 0x61
'mweobs',    # 0x62
'mweos',    # 0x63
'mweoss',    # 0x64
'mweong',    # 0x65
'mweoj',    # 0x66
'mweoc',    # 0x67
'mweok',    # 0x68
'mweot',    # 0x69
'mweop',    # 0x6a
'mweoh',    # 0x6b
'mwe',    # 0x6c
'mweg',    # 0x6d
'mwegg',    # 0x6e
'mwegs',    # 0x6f
'mwen',    # 0x70
'mwenj',    # 0x71
'mwenh',    # 0x72
'mwed',    # 0x73
'mwel',    # 0x74
'mwelg',    # 0x75
'mwelm',    # 0x76
'mwelb',    # 0x77
'mwels',    # 0x78
'mwelt',    # 0x79
'mwelp',    # 0x7a
'mwelh',    # 0x7b
'mwem',    # 0x7c
'mweb',    # 0x7d
'mwebs',    # 0x7e
'mwes',    # 0x7f
'mwess',    # 0x80
'mweng',    # 0x81
'mwej',    # 0x82
'mwec',    # 0x83
'mwek',    # 0x84
'mwet',    # 0x85
'mwep',    # 0x86
'mweh',    # 0x87
'mwi',    # 0x88
'mwig',    # 0x89
'mwigg',    # 0x8a
'mwigs',    # 0x8b
'mwin',    # 0x8c
'mwinj',    # 0x8d
'mwinh',    # 0x8e
'mwid',    # 0x8f
'mwil',    # 0x90
'mwilg',    # 0x91
'mwilm',    # 0x92
'mwilb',    # 0x93
'mwils',    # 0x94
'mwilt',    # 0x95
'mwilp',    # 0x96
'mwilh',    # 0x97
'mwim',    # 0x98
'mwib',    # 0x99
'mwibs',    # 0x9a
'mwis',    # 0x9b
'mwiss',    # 0x9c
'mwing',    # 0x9d
'mwij',    # 0x9e
'mwic',    # 0x9f
'mwik',    # 0xa0
'mwit',    # 0xa1
'mwip',    # 0xa2
'mwih',    # 0xa3
'myu',    # 0xa4
'myug',    # 0xa5
'myugg',    # 0xa6
'myugs',    # 0xa7
'myun',    # 0xa8
'myunj',    # 0xa9
'myunh',    # 0xaa
'myud',    # 0xab
'myul',    # 0xac
'myulg',    # 0xad
'myulm',    # 0xae
'myulb',    # 0xaf
'myuls',    # 0xb0
'myult',    # 0xb1
'myulp',    # 0xb2
'myulh',    # 0xb3
'myum',    # 0xb4
'myub',    # 0xb5
'myubs',    # 0xb6
'myus',    # 0xb7
'myuss',    # 0xb8
'myung',    # 0xb9
'myuj',    # 0xba
'myuc',    # 0xbb
'myuk',    # 0xbc
'myut',    # 0xbd
'myup',    # 0xbe
'myuh',    # 0xbf
'meu',    # 0xc0
'meug',    # 0xc1
'meugg',    # 0xc2
'meugs',    # 0xc3
'meun',    # 0xc4
'meunj',    # 0xc5
'meunh',    # 0xc6
'meud',    # 0xc7
'meul',    # 0xc8
'meulg',    # 0xc9
'meulm',    # 0xca
'meulb',    # 0xcb
'meuls',    # 0xcc
'meult',    # 0xcd
'meulp',    # 0xce
'meulh',    # 0xcf
'meum',    # 0xd0
'meub',    # 0xd1
'meubs',    # 0xd2
'meus',    # 0xd3
'meuss',    # 0xd4
'meung',    # 0xd5
'meuj',    # 0xd6
'meuc',    # 0xd7
'meuk',    # 0xd8
'meut',    # 0xd9
'meup',    # 0xda
'meuh',    # 0xdb
'myi',    # 0xdc
'myig',    # 0xdd
'myigg',    # 0xde
'myigs',    # 0xdf
'myin',    # 0xe0
'myinj',    # 0xe1
'myinh',    # 0xe2
'myid',    # 0xe3
'myil',    # 0xe4
'myilg',    # 0xe5
'myilm',    # 0xe6
'myilb',    # 0xe7
'myils',    # 0xe8
'myilt',    # 0xe9
'myilp',    # 0xea
'myilh',    # 0xeb
'myim',    # 0xec
'myib',    # 0xed
'myibs',    # 0xee
'myis',    # 0xef
'myiss',    # 0xf0
'mying',    # 0xf1
'myij',    # 0xf2
'myic',    # 0xf3
'myik',    # 0xf4
'myit',    # 0xf5
'myip',    # 0xf6
'myih',    # 0xf7
'mi',    # 0xf8
'mig',    # 0xf9
'migg',    # 0xfa
'migs',    # 0xfb
'min',    # 0xfc
'minj',    # 0xfd
'minh',    # 0xfe
'mid',    # 0xff
)
