data = (
'Zui ',    # 0x00
'Can ',    # 0x01
'Xu ',    # 0x02
'Hui ',    # 0x03
'<PERSON> ',    # 0x04
'Qie ',    # 0x05
'<PERSON> ',    # 0x06
'Pi ',    # 0x07
'Yue ',    # 0x08
'You ',    # 0x09
'Ruan ',    # 0x0a
'Peng ',    # 0x0b
'Ban ',    # 0x0c
'Fu ',    # 0x0d
'Ling ',    # 0x0e
'Fei ',    # 0x0f
'Qu ',    # 0x10
None,    # 0x11
'Nu ',    # 0x12
'Tiao ',    # 0x13
'Shuo ',    # 0x14
'Zhen ',    # 0x15
'<PERSON> ',    # 0x16
'<PERSON> ',    # 0x17
'<PERSON> ',    # 0x18
'Ming ',    # 0x19
'<PERSON> ',    # 0x1a
'<PERSON> ',    # 0x1b
'Tun ',    # 0x1c
'<PERSON> ',    # 0x1d
'Ji ',    # 0x1e
'Qi ',    # 0x1f
'Ying ',    # 0x20
'Zong ',    # 0x21
'<PERSON> ',    # 0x22
'Tong ',    # 0x23
'Lang ',    # 0x24
None,    # 0x25
'Meng ',    # 0x26
'Long ',    # 0x27
'Mu ',    # 0x28
'Deng ',    # 0x29
'Wei ',    # 0x2a
'Mo ',    # 0x2b
'Ben ',    # 0x2c
'Zha ',    # 0x2d
'Zhu ',    # 0x2e
'Zhu ',    # 0x2f
None,    # 0x30
'Zhu ',    # 0x31
'Ren ',    # 0x32
'Ba ',    # 0x33
'Po ',    # 0x34
'Duo ',    # 0x35
'Duo ',    # 0x36
'Dao ',    # 0x37
'Li ',    # 0x38
'Qiu ',    # 0x39
'Ji ',    # 0x3a
'Jiu ',    # 0x3b
'Bi ',    # 0x3c
'Xiu ',    # 0x3d
'Ting ',    # 0x3e
'Ci ',    # 0x3f
'Sha ',    # 0x40
'Eburi ',    # 0x41
'Za ',    # 0x42
'Quan ',    # 0x43
'Qian ',    # 0x44
'Yu ',    # 0x45
'Gan ',    # 0x46
'Wu ',    # 0x47
'Cha ',    # 0x48
'Shan ',    # 0x49
'Xun ',    # 0x4a
'Fan ',    # 0x4b
'Wu ',    # 0x4c
'Zi ',    # 0x4d
'Li ',    # 0x4e
'Xing ',    # 0x4f
'Cai ',    # 0x50
'Cun ',    # 0x51
'Ren ',    # 0x52
'Shao ',    # 0x53
'Tuo ',    # 0x54
'Di ',    # 0x55
'Zhang ',    # 0x56
'Mang ',    # 0x57
'Chi ',    # 0x58
'Yi ',    # 0x59
'Gu ',    # 0x5a
'Gong ',    # 0x5b
'Du ',    # 0x5c
'Yi ',    # 0x5d
'Qi ',    # 0x5e
'Shu ',    # 0x5f
'Gang ',    # 0x60
'Tiao ',    # 0x61
'Moku ',    # 0x62
'Soma ',    # 0x63
'Tochi ',    # 0x64
'Lai ',    # 0x65
'Sugi ',    # 0x66
'Mang ',    # 0x67
'Yang ',    # 0x68
'Ma ',    # 0x69
'Miao ',    # 0x6a
'Si ',    # 0x6b
'Yuan ',    # 0x6c
'Hang ',    # 0x6d
'Fei ',    # 0x6e
'Bei ',    # 0x6f
'Jie ',    # 0x70
'Dong ',    # 0x71
'Gao ',    # 0x72
'Yao ',    # 0x73
'Xian ',    # 0x74
'Chu ',    # 0x75
'Qun ',    # 0x76
'Pa ',    # 0x77
'Shu ',    # 0x78
'Hua ',    # 0x79
'Xin ',    # 0x7a
'Chou ',    # 0x7b
'Zhu ',    # 0x7c
'Chou ',    # 0x7d
'Song ',    # 0x7e
'Ban ',    # 0x7f
'Song ',    # 0x80
'Ji ',    # 0x81
'Yue ',    # 0x82
'Jin ',    # 0x83
'Gou ',    # 0x84
'Ji ',    # 0x85
'Mao ',    # 0x86
'Pi ',    # 0x87
'Bi ',    # 0x88
'Wang ',    # 0x89
'Ang ',    # 0x8a
'Fang ',    # 0x8b
'Fen ',    # 0x8c
'Yi ',    # 0x8d
'Fu ',    # 0x8e
'Nan ',    # 0x8f
'Xi ',    # 0x90
'Hu ',    # 0x91
'Ya ',    # 0x92
'Dou ',    # 0x93
'Xun ',    # 0x94
'Zhen ',    # 0x95
'Yao ',    # 0x96
'Lin ',    # 0x97
'Rui ',    # 0x98
'E ',    # 0x99
'Mei ',    # 0x9a
'Zhao ',    # 0x9b
'Guo ',    # 0x9c
'Zhi ',    # 0x9d
'Cong ',    # 0x9e
'Yun ',    # 0x9f
'Waku ',    # 0xa0
'Dou ',    # 0xa1
'Shu ',    # 0xa2
'Zao ',    # 0xa3
None,    # 0xa4
'Li ',    # 0xa5
'Haze ',    # 0xa6
'Jian ',    # 0xa7
'Cheng ',    # 0xa8
'Matsu ',    # 0xa9
'Qiang ',    # 0xaa
'Feng ',    # 0xab
'Nan ',    # 0xac
'Xiao ',    # 0xad
'Xian ',    # 0xae
'Ku ',    # 0xaf
'Ping ',    # 0xb0
'Yi ',    # 0xb1
'Xi ',    # 0xb2
'Zhi ',    # 0xb3
'Guai ',    # 0xb4
'Xiao ',    # 0xb5
'Jia ',    # 0xb6
'Jia ',    # 0xb7
'Gou ',    # 0xb8
'Fu ',    # 0xb9
'Mo ',    # 0xba
'Yi ',    # 0xbb
'Ye ',    # 0xbc
'Ye ',    # 0xbd
'Shi ',    # 0xbe
'Nie ',    # 0xbf
'Bi ',    # 0xc0
'Duo ',    # 0xc1
'Yi ',    # 0xc2
'Ling ',    # 0xc3
'Bing ',    # 0xc4
'Ni ',    # 0xc5
'La ',    # 0xc6
'He ',    # 0xc7
'Pan ',    # 0xc8
'Fan ',    # 0xc9
'Zhong ',    # 0xca
'Dai ',    # 0xcb
'Ci ',    # 0xcc
'Yang ',    # 0xcd
'Fu ',    # 0xce
'Bo ',    # 0xcf
'Mou ',    # 0xd0
'Gan ',    # 0xd1
'Qi ',    # 0xd2
'Ran ',    # 0xd3
'Rou ',    # 0xd4
'Mao ',    # 0xd5
'Zhao ',    # 0xd6
'Song ',    # 0xd7
'Zhe ',    # 0xd8
'Xia ',    # 0xd9
'You ',    # 0xda
'Shen ',    # 0xdb
'Ju ',    # 0xdc
'Tuo ',    # 0xdd
'Zuo ',    # 0xde
'Nan ',    # 0xdf
'Ning ',    # 0xe0
'Yong ',    # 0xe1
'Di ',    # 0xe2
'Zhi ',    # 0xe3
'Zha ',    # 0xe4
'Cha ',    # 0xe5
'Dan ',    # 0xe6
'Gu ',    # 0xe7
'Pu ',    # 0xe8
'Jiu ',    # 0xe9
'Ao ',    # 0xea
'Fu ',    # 0xeb
'Jian ',    # 0xec
'Bo ',    # 0xed
'Duo ',    # 0xee
'Ke ',    # 0xef
'Nai ',    # 0xf0
'Zhu ',    # 0xf1
'Bi ',    # 0xf2
'Liu ',    # 0xf3
'Chai ',    # 0xf4
'Zha ',    # 0xf5
'Si ',    # 0xf6
'Zhu ',    # 0xf7
'Pei ',    # 0xf8
'Shi ',    # 0xf9
'Guai ',    # 0xfa
'Cha ',    # 0xfb
'Yao ',    # 0xfc
'Jue ',    # 0xfd
'Jiu ',    # 0xfe
'Shi ',    # 0xff
)
