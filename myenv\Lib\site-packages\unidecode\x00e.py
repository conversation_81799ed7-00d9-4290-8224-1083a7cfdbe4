data = (
None,    # 0x00
'k',    # 0x01
'kh',    # 0x02
'kh',    # 0x03
'kh',    # 0x04
'kh',    # 0x05
'kh',    # 0x06
'ng',    # 0x07
'cch',    # 0x08
'ch',    # 0x09
'ch',    # 0x0a
'ch',    # 0x0b
'ch',    # 0x0c
'y',    # 0x0d
'd',    # 0x0e
't',    # 0x0f
'th',    # 0x10
'th',    # 0x11
'th',    # 0x12
'n',    # 0x13
'd',    # 0x14
't',    # 0x15
'th',    # 0x16
'th',    # 0x17
'th',    # 0x18
'n',    # 0x19
'b',    # 0x1a
'p',    # 0x1b
'ph',    # 0x1c
'f',    # 0x1d
'ph',    # 0x1e
'f',    # 0x1f
'ph',    # 0x20
'm',    # 0x21
'y',    # 0x22
'r',    # 0x23
'R',    # 0x24
'l',    # 0x25
'L',    # 0x26
'w',    # 0x27
's',    # 0x28
's',    # 0x29
's',    # 0x2a
'h',    # 0x2b
'l',    # 0x2c
'`',    # 0x2d
'h',    # 0x2e
'~',    # 0x2f
'a',    # 0x30
'a',    # 0x31
'aa',    # 0x32
'am',    # 0x33
'i',    # 0x34
'ii',    # 0x35
'ue',    # 0x36
'uue',    # 0x37
'u',    # 0x38
'uu',    # 0x39
'\'',    # 0x3a
None,    # 0x3b
None,    # 0x3c
None,    # 0x3d
None,    # 0x3e
'Bh.',    # 0x3f
'e',    # 0x40
'ae',    # 0x41
'o',    # 0x42
'ai',    # 0x43
'ai',    # 0x44
'ao',    # 0x45
'+',    # 0x46
'',    # 0x47
'',    # 0x48
'',    # 0x49
'',    # 0x4a
'',    # 0x4b
'',    # 0x4c
'M',    # 0x4d
'',    # 0x4e
' * ',    # 0x4f
'0',    # 0x50
'1',    # 0x51
'2',    # 0x52
'3',    # 0x53
'4',    # 0x54
'5',    # 0x55
'6',    # 0x56
'7',    # 0x57
'8',    # 0x58
'9',    # 0x59
' // ',    # 0x5a
' /// ',    # 0x5b
None,    # 0x5c
None,    # 0x5d
None,    # 0x5e
None,    # 0x5f
None,    # 0x60
None,    # 0x61
None,    # 0x62
None,    # 0x63
None,    # 0x64
None,    # 0x65
None,    # 0x66
None,    # 0x67
None,    # 0x68
None,    # 0x69
None,    # 0x6a
None,    # 0x6b
None,    # 0x6c
None,    # 0x6d
None,    # 0x6e
None,    # 0x6f
None,    # 0x70
None,    # 0x71
None,    # 0x72
None,    # 0x73
None,    # 0x74
None,    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
None,    # 0x80
'k',    # 0x81
'kh',    # 0x82
None,    # 0x83
'kh',    # 0x84
None,    # 0x85
None,    # 0x86
'ng',    # 0x87
'ch',    # 0x88
None,    # 0x89
's',    # 0x8a
None,    # 0x8b
None,    # 0x8c
'ny',    # 0x8d
None,    # 0x8e
None,    # 0x8f
None,    # 0x90
None,    # 0x91
None,    # 0x92
None,    # 0x93
'd',    # 0x94
'h',    # 0x95
'th',    # 0x96
'th',    # 0x97
None,    # 0x98
'n',    # 0x99
'b',    # 0x9a
'p',    # 0x9b
'ph',    # 0x9c
'f',    # 0x9d
'ph',    # 0x9e
'f',    # 0x9f
None,    # 0xa0
'm',    # 0xa1
'y',    # 0xa2
'r',    # 0xa3
None,    # 0xa4
'l',    # 0xa5
None,    # 0xa6
'w',    # 0xa7
None,    # 0xa8
None,    # 0xa9
's',    # 0xaa
'h',    # 0xab
None,    # 0xac
'`',    # 0xad
'',    # 0xae
'~',    # 0xaf
'a',    # 0xb0
'',    # 0xb1
'aa',    # 0xb2
'am',    # 0xb3
'i',    # 0xb4
'ii',    # 0xb5
'y',    # 0xb6
'yy',    # 0xb7
'u',    # 0xb8
'uu',    # 0xb9
None,    # 0xba
'o',    # 0xbb
'l',    # 0xbc
'ny',    # 0xbd
None,    # 0xbe
None,    # 0xbf
'e',    # 0xc0
'ei',    # 0xc1
'o',    # 0xc2
'ay',    # 0xc3
'ai',    # 0xc4
None,    # 0xc5
'+',    # 0xc6
None,    # 0xc7
'',    # 0xc8
'',    # 0xc9
'',    # 0xca
'',    # 0xcb
'',    # 0xcc
'M',    # 0xcd
None,    # 0xce
None,    # 0xcf
'0',    # 0xd0
'1',    # 0xd1
'2',    # 0xd2
'3',    # 0xd3
'4',    # 0xd4
'5',    # 0xd5
'6',    # 0xd6
'7',    # 0xd7
'8',    # 0xd8
'9',    # 0xd9
None,    # 0xda
None,    # 0xdb
'hn',    # 0xdc
'hm',    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
None,    # 0xe6
None,    # 0xe7
None,    # 0xe8
None,    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
