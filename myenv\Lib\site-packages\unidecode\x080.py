data = (
'<PERSON> ',    # 0x00
'Lao ',    # 0x01
None,    # 0x02
'Kao ',    # 0x03
'<PERSON> ',    # 0x04
'Zhe ',    # 0x05
'Qi ',    # 0x06
'Gou ',    # 0x07
'Gou ',    # 0x08
'Gou ',    # 0x09
'Die ',    # 0x0a
'Die ',    # 0x0b
'Er ',    # 0x0c
'Shua ',    # 0x0d
'Ruan ',    # 0x0e
'Er ',    # 0x0f
'Nai ',    # 0x10
'Zhuan ',    # 0x11
'Lei ',    # 0x12
'Ting ',    # 0x13
'Zi ',    # 0x14
'Geng ',    # 0x15
'Chao ',    # 0x16
'Hao ',    # 0x17
'Yun ',    # 0x18
'Pa ',    # 0x19
'Pi ',    # 0x1a
'Chi ',    # 0x1b
'Si ',    # 0x1c
'Chu ',    # 0x1d
'Jia ',    # 0x1e
'Ju ',    # 0x1f
'He ',    # 0x20
'Chu ',    # 0x21
'Lao ',    # 0x22
'Lun ',    # 0x23
'Ji ',    # 0x24
'Tang ',    # 0x25
'Ou ',    # 0x26
'Lou ',    # 0x27
'Nou ',    # 0x28
'Gou ',    # 0x29
'Pang ',    # 0x2a
'Ze ',    # 0x2b
'Lou ',    # 0x2c
'Ji ',    # 0x2d
'Lao ',    # 0x2e
'Huo ',    # 0x2f
'You ',    # 0x30
'Mo ',    # 0x31
'Huai ',    # 0x32
'Er ',    # 0x33
'Zhe ',    # 0x34
'Ting ',    # 0x35
'Ye ',    # 0x36
'Da ',    # 0x37
'Song ',    # 0x38
'Qin ',    # 0x39
'Yun ',    # 0x3a
'Chi ',    # 0x3b
'Dan ',    # 0x3c
'Dan ',    # 0x3d
'Hong ',    # 0x3e
'Geng ',    # 0x3f
'Zhi ',    # 0x40
None,    # 0x41
'Nie ',    # 0x42
'Dan ',    # 0x43
'Zhen ',    # 0x44
'Che ',    # 0x45
'Ling ',    # 0x46
'Zheng ',    # 0x47
'You ',    # 0x48
'Wa ',    # 0x49
'Liao ',    # 0x4a
'Long ',    # 0x4b
'Zhi ',    # 0x4c
'Ning ',    # 0x4d
'Tiao ',    # 0x4e
'Er ',    # 0x4f
'Ya ',    # 0x50
'Die ',    # 0x51
'Gua ',    # 0x52
None,    # 0x53
'Lian ',    # 0x54
'Hao ',    # 0x55
'Sheng ',    # 0x56
'Lie ',    # 0x57
'Pin ',    # 0x58
'Jing ',    # 0x59
'Ju ',    # 0x5a
'Bi ',    # 0x5b
'Di ',    # 0x5c
'Guo ',    # 0x5d
'Wen ',    # 0x5e
'Xu ',    # 0x5f
'Ping ',    # 0x60
'Cong ',    # 0x61
'Shikato ',    # 0x62
None,    # 0x63
'Ting ',    # 0x64
'Yu ',    # 0x65
'Cong ',    # 0x66
'Kui ',    # 0x67
'Tsuraneru ',    # 0x68
'Kui ',    # 0x69
'Cong ',    # 0x6a
'Lian ',    # 0x6b
'Weng ',    # 0x6c
'Kui ',    # 0x6d
'Lian ',    # 0x6e
'Lian ',    # 0x6f
'Cong ',    # 0x70
'Ao ',    # 0x71
'Sheng ',    # 0x72
'Song ',    # 0x73
'Ting ',    # 0x74
'Kui ',    # 0x75
'Nie ',    # 0x76
'Zhi ',    # 0x77
'Dan ',    # 0x78
'Ning ',    # 0x79
'Qie ',    # 0x7a
'Ji ',    # 0x7b
'Ting ',    # 0x7c
'Ting ',    # 0x7d
'Long ',    # 0x7e
'Yu ',    # 0x7f
'Yu ',    # 0x80
'Zhao ',    # 0x81
'Si ',    # 0x82
'Su ',    # 0x83
'Yi ',    # 0x84
'Su ',    # 0x85
'Si ',    # 0x86
'Zhao ',    # 0x87
'Zhao ',    # 0x88
'Rou ',    # 0x89
'Yi ',    # 0x8a
'Le ',    # 0x8b
'Ji ',    # 0x8c
'Qiu ',    # 0x8d
'Ken ',    # 0x8e
'Cao ',    # 0x8f
'Ge ',    # 0x90
'Di ',    # 0x91
'Huan ',    # 0x92
'Huang ',    # 0x93
'Yi ',    # 0x94
'Ren ',    # 0x95
'Xiao ',    # 0x96
'Ru ',    # 0x97
'Zhou ',    # 0x98
'Yuan ',    # 0x99
'Du ',    # 0x9a
'Gang ',    # 0x9b
'Rong ',    # 0x9c
'Gan ',    # 0x9d
'Cha ',    # 0x9e
'Wo ',    # 0x9f
'Chang ',    # 0xa0
'Gu ',    # 0xa1
'Zhi ',    # 0xa2
'Han ',    # 0xa3
'Fu ',    # 0xa4
'Fei ',    # 0xa5
'Fen ',    # 0xa6
'Pei ',    # 0xa7
'Pang ',    # 0xa8
'Jian ',    # 0xa9
'Fang ',    # 0xaa
'Zhun ',    # 0xab
'You ',    # 0xac
'Na ',    # 0xad
'Hang ',    # 0xae
'Ken ',    # 0xaf
'Ran ',    # 0xb0
'Gong ',    # 0xb1
'Yu ',    # 0xb2
'Wen ',    # 0xb3
'Yao ',    # 0xb4
'Jin ',    # 0xb5
'Pi ',    # 0xb6
'Qian ',    # 0xb7
'Xi ',    # 0xb8
'Xi ',    # 0xb9
'Fei ',    # 0xba
'Ken ',    # 0xbb
'Jing ',    # 0xbc
'Tai ',    # 0xbd
'Shen ',    # 0xbe
'Zhong ',    # 0xbf
'Zhang ',    # 0xc0
'Xie ',    # 0xc1
'Shen ',    # 0xc2
'Wei ',    # 0xc3
'Zhou ',    # 0xc4
'Die ',    # 0xc5
'Dan ',    # 0xc6
'Fei ',    # 0xc7
'Ba ',    # 0xc8
'Bo ',    # 0xc9
'Qu ',    # 0xca
'Tian ',    # 0xcb
'Bei ',    # 0xcc
'Gua ',    # 0xcd
'Tai ',    # 0xce
'Zi ',    # 0xcf
'Ku ',    # 0xd0
'Zhi ',    # 0xd1
'Ni ',    # 0xd2
'Ping ',    # 0xd3
'Zi ',    # 0xd4
'Fu ',    # 0xd5
'Pang ',    # 0xd6
'Zhen ',    # 0xd7
'Xian ',    # 0xd8
'Zuo ',    # 0xd9
'Pei ',    # 0xda
'Jia ',    # 0xdb
'Sheng ',    # 0xdc
'Zhi ',    # 0xdd
'Bao ',    # 0xde
'Mu ',    # 0xdf
'Qu ',    # 0xe0
'Hu ',    # 0xe1
'Ke ',    # 0xe2
'Yi ',    # 0xe3
'Yin ',    # 0xe4
'Xu ',    # 0xe5
'Yang ',    # 0xe6
'Long ',    # 0xe7
'Dong ',    # 0xe8
'Ka ',    # 0xe9
'Lu ',    # 0xea
'Jing ',    # 0xeb
'Nu ',    # 0xec
'Yan ',    # 0xed
'Pang ',    # 0xee
'Kua ',    # 0xef
'Yi ',    # 0xf0
'Guang ',    # 0xf1
'Gai ',    # 0xf2
'Ge ',    # 0xf3
'Dong ',    # 0xf4
'Zhi ',    # 0xf5
'Xiao ',    # 0xf6
'Xiong ',    # 0xf7
'Xiong ',    # 0xf8
'Er ',    # 0xf9
'E ',    # 0xfa
'Xing ',    # 0xfb
'Pian ',    # 0xfc
'Neng ',    # 0xfd
'Zi ',    # 0xfe
'Gui ',    # 0xff
)
