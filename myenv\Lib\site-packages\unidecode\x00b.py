data = (
None,    # 0x00
'N',    # 0x01
'N',    # 0x02
'H',    # 0x03
None,    # 0x04
'a',    # 0x05
'aa',    # 0x06
'i',    # 0x07
'ii',    # 0x08
'u',    # 0x09
'uu',    # 0x0a
'R',    # 0x0b
'L',    # 0x0c
None,    # 0x0d
None,    # 0x0e
'e',    # 0x0f
'ai',    # 0x10
None,    # 0x11
None,    # 0x12
'o',    # 0x13
'au',    # 0x14
'k',    # 0x15
'kh',    # 0x16
'g',    # 0x17
'gh',    # 0x18
'ng',    # 0x19
'c',    # 0x1a
'ch',    # 0x1b
'j',    # 0x1c
'jh',    # 0x1d
'ny',    # 0x1e
'tt',    # 0x1f
'tth',    # 0x20
'dd',    # 0x21
'ddh',    # 0x22
'nn',    # 0x23
't',    # 0x24
'th',    # 0x25
'd',    # 0x26
'dh',    # 0x27
'n',    # 0x28
None,    # 0x29
'p',    # 0x2a
'ph',    # 0x2b
'b',    # 0x2c
'bh',    # 0x2d
'm',    # 0x2e
'y',    # 0x2f
'r',    # 0x30
None,    # 0x31
'l',    # 0x32
'll',    # 0x33
None,    # 0x34
'',    # 0x35
'sh',    # 0x36
'ss',    # 0x37
's',    # 0x38
'h',    # 0x39
None,    # 0x3a
None,    # 0x3b
'\'',    # 0x3c
'\'',    # 0x3d
'aa',    # 0x3e
'i',    # 0x3f
'ii',    # 0x40
'u',    # 0x41
'uu',    # 0x42
'R',    # 0x43
None,    # 0x44
None,    # 0x45
None,    # 0x46
'e',    # 0x47
'ai',    # 0x48
None,    # 0x49
None,    # 0x4a
'o',    # 0x4b
'au',    # 0x4c
'',    # 0x4d
None,    # 0x4e
None,    # 0x4f
None,    # 0x50
None,    # 0x51
None,    # 0x52
None,    # 0x53
None,    # 0x54
None,    # 0x55
'+',    # 0x56
'+',    # 0x57
None,    # 0x58
None,    # 0x59
None,    # 0x5a
None,    # 0x5b
'rr',    # 0x5c
'rh',    # 0x5d
None,    # 0x5e
'yy',    # 0x5f
'RR',    # 0x60
'LL',    # 0x61
None,    # 0x62
None,    # 0x63
None,    # 0x64
None,    # 0x65
'0',    # 0x66
'1',    # 0x67
'2',    # 0x68
'3',    # 0x69
'4',    # 0x6a
'5',    # 0x6b
'6',    # 0x6c
'7',    # 0x6d
'8',    # 0x6e
'9',    # 0x6f
'',    # 0x70
None,    # 0x71
None,    # 0x72
None,    # 0x73
None,    # 0x74
None,    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
None,    # 0x80
None,    # 0x81
'N',    # 0x82
'H',    # 0x83
None,    # 0x84
'a',    # 0x85
'aa',    # 0x86
'i',    # 0x87
'ii',    # 0x88
'u',    # 0x89
'uu',    # 0x8a
None,    # 0x8b
None,    # 0x8c
None,    # 0x8d
'e',    # 0x8e
'ee',    # 0x8f
'ai',    # 0x90
None,    # 0x91
'o',    # 0x92
'oo',    # 0x93
'au',    # 0x94
'k',    # 0x95
None,    # 0x96
None,    # 0x97
None,    # 0x98
'ng',    # 0x99
'c',    # 0x9a
None,    # 0x9b
'j',    # 0x9c
None,    # 0x9d
'ny',    # 0x9e
'tt',    # 0x9f
None,    # 0xa0
None,    # 0xa1
None,    # 0xa2
'nn',    # 0xa3
't',    # 0xa4
None,    # 0xa5
None,    # 0xa6
None,    # 0xa7
'n',    # 0xa8
'nnn',    # 0xa9
'p',    # 0xaa
None,    # 0xab
None,    # 0xac
None,    # 0xad
'm',    # 0xae
'y',    # 0xaf
'r',    # 0xb0
'rr',    # 0xb1
'l',    # 0xb2
'll',    # 0xb3
'lll',    # 0xb4
'v',    # 0xb5
None,    # 0xb6
'ss',    # 0xb7
's',    # 0xb8
'h',    # 0xb9
None,    # 0xba
None,    # 0xbb
None,    # 0xbc
None,    # 0xbd
'aa',    # 0xbe
'i',    # 0xbf
'ii',    # 0xc0
'u',    # 0xc1
'uu',    # 0xc2
None,    # 0xc3
None,    # 0xc4
None,    # 0xc5
'e',    # 0xc6
'ee',    # 0xc7
'ai',    # 0xc8
None,    # 0xc9
'o',    # 0xca
'oo',    # 0xcb
'au',    # 0xcc
'',    # 0xcd
None,    # 0xce
None,    # 0xcf
None,    # 0xd0
None,    # 0xd1
None,    # 0xd2
None,    # 0xd3
None,    # 0xd4
None,    # 0xd5
None,    # 0xd6
'+',    # 0xd7
None,    # 0xd8
None,    # 0xd9
None,    # 0xda
None,    # 0xdb
None,    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
'0',    # 0xe6
'1',    # 0xe7
'2',    # 0xe8
'3',    # 0xe9
'4',    # 0xea
'5',    # 0xeb
'6',    # 0xec
'7',    # 0xed
'8',    # 0xee
'9',    # 0xef
'+10+',    # 0xf0
'+100+',    # 0xf1
'+1000+',    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
