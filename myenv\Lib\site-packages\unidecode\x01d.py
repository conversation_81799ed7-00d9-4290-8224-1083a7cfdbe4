data = (
'A',    # 0x00
'AE',    # 0x01
'ae',    # 0x02
'B',    # 0x03
'C',    # 0x04
'D',    # 0x05
'D',    # 0x06
'E',    # 0x07
'e',    # 0x08
'i',    # 0x09
'J',    # 0x0a
'K',    # 0x0b
'L',    # 0x0c
'M',    # 0x0d
'N',    # 0x0e
'O',    # 0x0f
'',    # 0x10
'O',    # 0x11
'',    # 0x12
'O',    # 0x13
'Oe',    # 0x14
'Ou',    # 0x15
'',    # 0x16
'',    # 0x17
'P',    # 0x18
'R',    # 0x19
'R',    # 0x1a
'T',    # 0x1b
'U',    # 0x1c
'u',    # 0x1d
'u',    # 0x1e
'm',    # 0x1f
'V',    # 0x20
'W',    # 0x21
'Z',    # 0x22
'',    # 0x23
'',    # 0x24
'',    # 0x25
'',    # 0x26
'',    # 0x27
'',    # 0x28
'',    # 0x29
'',    # 0x2a
'',    # 0x2b
'A',    # 0x2c
'AE',    # 0x2d
'B',    # 0x2e
'B',    # 0x2f
'D',    # 0x30
'E',    # 0x31
'E',    # 0x32
'G',    # 0x33
'H',    # 0x34
'I',    # 0x35
'J',    # 0x36
'K',    # 0x37
'L',    # 0x38
'M',    # 0x39
'N',    # 0x3a
'N',    # 0x3b
'O',    # 0x3c
'Ou',    # 0x3d
'P',    # 0x3e
'R',    # 0x3f
'T',    # 0x40
'U',    # 0x41
'W',    # 0x42
'a',    # 0x43
'a',    # 0x44
'a',    # 0x45
'ae',    # 0x46
'b',    # 0x47
'd',    # 0x48
'e',    # 0x49
'',    # 0x4a
'e',    # 0x4b
'e',    # 0x4c
'g',    # 0x4d
'i',    # 0x4e
'k',    # 0x4f
'm',    # 0x50
'',    # 0x51
'o',    # 0x52
'',    # 0x53
'',    # 0x54
'',    # 0x55
'p',    # 0x56
't',    # 0x57
'u',    # 0x58
'u',    # 0x59
'm',    # 0x5a
'v',    # 0x5b
'',    # 0x5c
'b',    # 0x5d
'g',    # 0x5e
'd',    # 0x5f
'f',    # 0x60
'',    # 0x61
'i',    # 0x62
'r',    # 0x63
'u',    # 0x64
'v',    # 0x65
'b',    # 0x66
'g',    # 0x67
'r',    # 0x68
'f',    # 0x69
'',    # 0x6a
'',    # 0x6b
'b',    # 0x6c
'd',    # 0x6d
'f',    # 0x6e
'm',    # 0x6f
'n',    # 0x70
'p',    # 0x71
'r',    # 0x72
'r',    # 0x73
's',    # 0x74
't',    # 0x75
'z',    # 0x76
'g',    # 0x77
'',    # 0x78
'',    # 0x79
'',    # 0x7a
'',    # 0x7b
'',    # 0x7c
'p',    # 0x7d
'',    # 0x7e
'',    # 0x7f
'b',    # 0x80
'd',    # 0x81
'f',    # 0x82
'g',    # 0x83
'k',    # 0x84
'l',    # 0x85
'm',    # 0x86
'n',    # 0x87
'p',    # 0x88
'r',    # 0x89
's',    # 0x8a
'',    # 0x8b
'v',    # 0x8c
'x',    # 0x8d
'z',    # 0x8e
'',    # 0x8f
'',    # 0x90
'',    # 0x91
'',    # 0x92
'',    # 0x93
'',    # 0x94
'',    # 0x95
'',    # 0x96
'',    # 0x97
'',    # 0x98
'',    # 0x99
'',    # 0x9a
'',    # 0x9b
'',    # 0x9c
'',    # 0x9d
'',    # 0x9e
'',    # 0x9f
'',    # 0xa0
'',    # 0xa1
'',    # 0xa2
'',    # 0xa3
'',    # 0xa4
'',    # 0xa5
'',    # 0xa6
'',    # 0xa7
'',    # 0xa8
'',    # 0xa9
'',    # 0xaa
'',    # 0xab
'',    # 0xac
'',    # 0xad
'',    # 0xae
'',    # 0xaf
'',    # 0xb0
'',    # 0xb1
'',    # 0xb2
'',    # 0xb3
'',    # 0xb4
'',    # 0xb5
'',    # 0xb6
'',    # 0xb7
'',    # 0xb8
'',    # 0xb9
'',    # 0xba
'',    # 0xbb
'',    # 0xbc
'',    # 0xbd
'',    # 0xbe
'',    # 0xbf
'',    # 0xc0
'',    # 0xc1
'',    # 0xc2
'',    # 0xc3
'',    # 0xc4
'',    # 0xc5
'',    # 0xc6
'',    # 0xc7
'',    # 0xc8
'',    # 0xc9
'',    # 0xca
'',    # 0xcb
'',    # 0xcc
'',    # 0xcd
'',    # 0xce
'',    # 0xcf
'',    # 0xd0
'',    # 0xd1
'',    # 0xd2
'',    # 0xd3
'',    # 0xd4
'',    # 0xd5
'',    # 0xd6
'',    # 0xd7
'',    # 0xd8
'',    # 0xd9
'',    # 0xda
'',    # 0xdb
'',    # 0xdc
'',    # 0xdd
'',    # 0xde
'',    # 0xdf
'',    # 0xe0
'',    # 0xe1
'',    # 0xe2
'',    # 0xe3
'',    # 0xe4
'',    # 0xe5
'',    # 0xe6
'',    # 0xe7
'',    # 0xe8
'',    # 0xe9
'',    # 0xea
'',    # 0xeb
'',    # 0xec
'',    # 0xed
'',    # 0xee
'',    # 0xef
'',    # 0xf0
'',    # 0xf1
'',    # 0xf2
'',    # 0xf3
'',    # 0xf4
'',    # 0xf5
'',    # 0xf6
'',    # 0xf7
'',    # 0xf8
'',    # 0xf9
'',    # 0xfa
'',    # 0xfb
'',    # 0xfc
'',    # 0xfd
'',    # 0xfe
)
