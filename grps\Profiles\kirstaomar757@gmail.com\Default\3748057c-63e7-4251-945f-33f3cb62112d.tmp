{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "fr-FR"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": false}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"check_default_browser": false, "window_placement": {"bottom": 1382, "left": 10, "maximized": true, "right": 1275, "top": 10, "work_area_bottom": 1392, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 19777, "default_search_provider": {"guid": ""}, "distribution": {"import_bookmarks": false, "import_history": false, "import_search_engine": false, "make_chrome_default_for_user": false, "skip_first_run_ui": true}, "dns_prefetching": {"enabled": false}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "4f950518-cdb8-43d2-8da1-9beb8e5260b0", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Découvrez des applications, des jeux, des extensions et des thèmes exceptionnels pour Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.101\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "gaia_cookie": {"changed_time": **********.850272, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "e13faebe-6947-4bc2-b670-fa4cf2fb8494"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "fr-FR,fr,en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"fr": 2}, "media": {"device_id_salt": "BB1367F2A7BF7283F06660EA0AF2706F", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "x+aM2xnLhBnov8jdBLGcNHBS5urH+9BCGd27/ATI4reSIrQKJ0cuNV2XT3vwo/QXPMlAiBkiAduBZPVqZHO9yQ=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PRICE_TRACKING": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "pinned_tabs": [], "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://groups.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [4, 5, 9, 10, 11, 13, 14, 15, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 2}}, "https://www.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3397086212619264e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3397086265484232e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 5.1, "rawScore": 5.1}}, "https://www.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3397086261217716e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pattern_pairs": {"https://*,*": {"media-stream": {"audio": "<PERSON><PERSON><PERSON>", "video": "<PERSON><PERSON><PERSON>"}}}, "pref_version": 1}, "creation_time": "13397086212225824", "default_content_setting_values": {"geolocation": 1}, "default_content_settings": {"geolocation": 1, "mouselock": 1, "notifications": 1, "popups": 1, "ppapi-broker": 1}, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.370091, "last_time_password_store_metrics_reported": **********.360919, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Votre Chrome", "password_hash_data_list": [], "password_manager_enabled": false, "were_old_google_logins_removed": true}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "E901D14E1E7A44955554E13F48FFF093695648796DAE7D31282FA0CAB296EAFD"}, "extensions": {"ui": {"developer_mode": "2A2E84C56306EE1CF1F395D894F347D2EA39B5EE9AC5AFE1119F8141B51E1206"}}, "homepage": "EEEE757F2E0CBEC400700A1DF68A81BB2310F287A908E6278B189C375AD6FD16", "homepage_is_newtabpage": "3C55DC5F4E25B9F751A7775F473488AAD19863AD5AB14D9851DF670298C51501", "session": {"restore_on_startup": "816D5B361542E2042E7373D73D187CAEFD0E2AD8979DF51E31EFFFC2D36BC254", "startup_urls": "AD28E862A29B5F005CFA6E3BA407492ECD498D8EE33C0A24921C5C2394A54BDD"}}, "browser": {"show_home_button": "32ECB00B3FCF13CE04850999204AF2770DB453E3AADC8C3F2DAAB8DF741FBD7F"}, "default_search_provider_data": {"template_url_data": "69FF187E761AD997C3B49E7ABD9EBE298683D9C3D65FFCBF2159F1D64B315CDD"}, "enterprise_signin": {"policy_recovery_token": "8F57FFB1DA04E8F71C3C301DAE1006F92CD62899DE294B37024BBC2B253B30FD"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "94E6446557D4B663B5212E0D2084A976ACC935362E7D85B89ECC9F92C1DDFF30", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "C4DF7F77A95B89F32B553206FE052C343092E52F07E7697D4997C8B8438966A0"}, "ui": {"developer_mode": "6BAC458EDE30406E1F89B7F550BF573191176E5BADA9AAE643238A4BE69A4DD5"}}, "google": {"services": {"account_id": "78297E717B4F0D8C4DB38FF80256FC36408E8340E1F460A18BD29997BB1D2A6F", "last_signed_in_username": "3782B3ACB569E58EDF06D4D2AF4536EE5FB3332C1E3B9FA9A0D79C70DCAF389B", "last_username": "06457CF5F09FEA4BA61C4ADB8CFDB6E8B3D20CB5729BC9C30182D3EDADF97501"}}, "homepage": "5F27C48BDE1C128CA64A7BBB7A7B4E045056D7C0EA0583CA887B764E7502A189", "homepage_is_newtabpage": "0394B4307D5324E26FEAF24C4E56CAFA0069EA52A220AF6DC1F01C65FF4A6B55", "media": {"cdm": {"origin_data": "D3E4DDD6B912E7B2AC918C92BED7A0FEBD184DC12ACC1262A60FFD1ED571C18A"}, "storage_id_salt": "141725210EBF9E43C9B91843DD570453634226B60B43F84CFC95DDAF00A17839"}, "module_blocklist_cache_md5_digest": "39F6FAF5924976B2090CBC5C17F58EBA45491976BFECFFB2A2E3EDFE7CD30395", "pinned_tabs": "F24F4AE46DBDAD21121F3FD66D65EFF761A60CD15F3ED17B6C25FD1B93E8B79D", "prefs": {"preference_reset_time": "43A78BEE012C387C6F75BB9F0D48CD840F5B53B7D1005499D7E14CAF0D9FA789"}, "safebrowsing": {"incidents_sent": "8B66E917E80A910601717A547611E57302C29F38AC72B3D0992594780C38F00B"}, "search_provider_overrides": "69281E718DED1C11A1458EF0C8915E892FABF6007D9B796129F84CA80676C0C4", "session": {"restore_on_startup": "26FB1A156DE37DAD86723D7798A2EE59FD991425AA77D2BD68B5921F7F3134FB", "startup_urls": "1C8688DC44334F5790CBE716902071B0F7CB63307808ABD8DC95028D513E3EE6"}}}, "safebrowsing": {"enabled": false, "event_timestamps": {}, "metrics_last_log_time": "13397086212", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "search": {"suggest_enabled": false}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ9+6TqMOS5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEI3vk6jDkuYX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "*****************", "uma_in_sql_start_time": "*****************"}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "sync": {"passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate": {"enabled": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}