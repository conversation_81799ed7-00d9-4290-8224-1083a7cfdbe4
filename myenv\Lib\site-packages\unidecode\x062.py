data = (
'Lian ',    # 0x00
'Nan ',    # 0x01
'Mi ',    # 0x02
'Tang ',    # 0x03
'<PERSON>e ',    # 0x04
'<PERSON> ',    # 0x05
'<PERSON> ',    # 0x06
'<PERSON> ',    # 0x07
'Ge ',    # 0x08
'Yue ',    # 0x09
'<PERSON> ',    # 0x0a
'Jian ',    # 0x0b
'Xu ',    # 0x0c
'Shu ',    # 0x0d
'Rong ',    # 0x0e
'Xi ',    # 0x0f
'<PERSON> ',    # 0x10
'Wo ',    # 0x11
'Jie ',    # 0x12
'Ge ',    # 0x13
'Jian ',    # 0x14
'Qiang ',    # 0x15
'Huo ',    # 0x16
'Qiang ',    # 0x17
'<PERSON>han ',    # 0x18
'Dong ',    # 0x19
'Qi ',    # 0x1a
'Jia ',    # 0x1b
'Die ',    # 0x1c
'Zei ',    # 0x1d
'Jia ',    # 0x1e
'Ji ',    # 0x1f
'Shi ',    # 0x20
'Kan ',    # 0x21
'Ji ',    # 0x22
'Kui ',    # 0x23
'Gai ',    # 0x24
'Deng ',    # 0x25
'<PERSON>han ',    # 0x26
'Chuang ',    # 0x27
'Ge ',    # 0x28
'Jian ',    # 0x29
'Jie ',    # 0x2a
'Yu ',    # 0x2b
'Jian ',    # 0x2c
'Yan ',    # 0x2d
'Lu ',    # 0x2e
'Xi ',    # 0x2f
'Zhan ',    # 0x30
'Xi ',    # 0x31
'Xi ',    # 0x32
'Chuo ',    # 0x33
'Dai ',    # 0x34
'Qu ',    # 0x35
'Hu ',    # 0x36
'Hu ',    # 0x37
'Hu ',    # 0x38
'E ',    # 0x39
'Shi ',    # 0x3a
'Li ',    # 0x3b
'Mao ',    # 0x3c
'Hu ',    # 0x3d
'Li ',    # 0x3e
'Fang ',    # 0x3f
'Suo ',    # 0x40
'Bian ',    # 0x41
'Dian ',    # 0x42
'Jiong ',    # 0x43
'Shang ',    # 0x44
'Yi ',    # 0x45
'Yi ',    # 0x46
'Shan ',    # 0x47
'Hu ',    # 0x48
'Fei ',    # 0x49
'Yan ',    # 0x4a
'Shou ',    # 0x4b
'T ',    # 0x4c
'Cai ',    # 0x4d
'Zha ',    # 0x4e
'Qiu ',    # 0x4f
'Le ',    # 0x50
'Bu ',    # 0x51
'Ba ',    # 0x52
'Da ',    # 0x53
'Reng ',    # 0x54
'Fu ',    # 0x55
'Hameru ',    # 0x56
'Zai ',    # 0x57
'Tuo ',    # 0x58
'Zhang ',    # 0x59
'Diao ',    # 0x5a
'Kang ',    # 0x5b
'Yu ',    # 0x5c
'Ku ',    # 0x5d
'Han ',    # 0x5e
'Shen ',    # 0x5f
'Cha ',    # 0x60
'Yi ',    # 0x61
'Gu ',    # 0x62
'Kou ',    # 0x63
'Wu ',    # 0x64
'Tuo ',    # 0x65
'Qian ',    # 0x66
'Zhi ',    # 0x67
'Ren ',    # 0x68
'Kuo ',    # 0x69
'Men ',    # 0x6a
'Sao ',    # 0x6b
'Yang ',    # 0x6c
'Niu ',    # 0x6d
'Ban ',    # 0x6e
'Che ',    # 0x6f
'Rao ',    # 0x70
'Xi ',    # 0x71
'Qian ',    # 0x72
'Ban ',    # 0x73
'Jia ',    # 0x74
'Yu ',    # 0x75
'Fu ',    # 0x76
'Ao ',    # 0x77
'Xi ',    # 0x78
'Pi ',    # 0x79
'Zhi ',    # 0x7a
'Zi ',    # 0x7b
'E ',    # 0x7c
'Dun ',    # 0x7d
'Zhao ',    # 0x7e
'Cheng ',    # 0x7f
'Ji ',    # 0x80
'Yan ',    # 0x81
'Kuang ',    # 0x82
'Bian ',    # 0x83
'Chao ',    # 0x84
'Ju ',    # 0x85
'Wen ',    # 0x86
'Hu ',    # 0x87
'Yue ',    # 0x88
'Jue ',    # 0x89
'Ba ',    # 0x8a
'Qin ',    # 0x8b
'Zhen ',    # 0x8c
'Zheng ',    # 0x8d
'Yun ',    # 0x8e
'Wan ',    # 0x8f
'Nu ',    # 0x90
'Yi ',    # 0x91
'Shu ',    # 0x92
'Zhua ',    # 0x93
'Pou ',    # 0x94
'Tou ',    # 0x95
'Dou ',    # 0x96
'Kang ',    # 0x97
'Zhe ',    # 0x98
'Pou ',    # 0x99
'Fu ',    # 0x9a
'Pao ',    # 0x9b
'Ba ',    # 0x9c
'Ao ',    # 0x9d
'Ze ',    # 0x9e
'Tuan ',    # 0x9f
'Kou ',    # 0xa0
'Lun ',    # 0xa1
'Qiang ',    # 0xa2
None,    # 0xa3
'Hu ',    # 0xa4
'Bao ',    # 0xa5
'Bing ',    # 0xa6
'Zhi ',    # 0xa7
'Peng ',    # 0xa8
'Tan ',    # 0xa9
'Pu ',    # 0xaa
'Pi ',    # 0xab
'Tai ',    # 0xac
'Yao ',    # 0xad
'Zhen ',    # 0xae
'Zha ',    # 0xaf
'Yang ',    # 0xb0
'Bao ',    # 0xb1
'He ',    # 0xb2
'Ni ',    # 0xb3
'Yi ',    # 0xb4
'Di ',    # 0xb5
'Chi ',    # 0xb6
'Pi ',    # 0xb7
'Za ',    # 0xb8
'Mo ',    # 0xb9
'Mo ',    # 0xba
'Shen ',    # 0xbb
'Ya ',    # 0xbc
'Chou ',    # 0xbd
'Qu ',    # 0xbe
'Min ',    # 0xbf
'Chu ',    # 0xc0
'Jia ',    # 0xc1
'Fu ',    # 0xc2
'Zhan ',    # 0xc3
'Zhu ',    # 0xc4
'Dan ',    # 0xc5
'Chai ',    # 0xc6
'Mu ',    # 0xc7
'Nian ',    # 0xc8
'La ',    # 0xc9
'Fu ',    # 0xca
'Pao ',    # 0xcb
'Ban ',    # 0xcc
'Pai ',    # 0xcd
'Ling ',    # 0xce
'Na ',    # 0xcf
'Guai ',    # 0xd0
'Qian ',    # 0xd1
'Ju ',    # 0xd2
'Tuo ',    # 0xd3
'Ba ',    # 0xd4
'Tuo ',    # 0xd5
'Tuo ',    # 0xd6
'Ao ',    # 0xd7
'Ju ',    # 0xd8
'Zhuo ',    # 0xd9
'Pan ',    # 0xda
'Zhao ',    # 0xdb
'Bai ',    # 0xdc
'Bai ',    # 0xdd
'Di ',    # 0xde
'Ni ',    # 0xdf
'Ju ',    # 0xe0
'Kuo ',    # 0xe1
'Long ',    # 0xe2
'Jian ',    # 0xe3
None,    # 0xe4
'Yong ',    # 0xe5
'Lan ',    # 0xe6
'Ning ',    # 0xe7
'Bo ',    # 0xe8
'Ze ',    # 0xe9
'Qian ',    # 0xea
'Hen ',    # 0xeb
'Gua ',    # 0xec
'Shi ',    # 0xed
'Jie ',    # 0xee
'Zheng ',    # 0xef
'Nin ',    # 0xf0
'Gong ',    # 0xf1
'Gong ',    # 0xf2
'Quan ',    # 0xf3
'Shuan ',    # 0xf4
'Cun ',    # 0xf5
'Zan ',    # 0xf6
'Kao ',    # 0xf7
'Chi ',    # 0xf8
'Xie ',    # 0xf9
'Ce ',    # 0xfa
'Hui ',    # 0xfb
'Pin ',    # 0xfc
'Zhuai ',    # 0xfd
'Shi ',    # 0xfe
'Na ',    # 0xff
)
