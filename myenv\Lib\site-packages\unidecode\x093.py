data = (
'Lun ',    # 0x00
'Kua ',    # 0x01
'Ling ',    # 0x02
'Bei ',    # 0x03
'Lu ',    # 0x04
'Li ',    # 0x05
'Qiang ',    # 0x06
'Pou ',    # 0x07
'<PERSON> ',    # 0x08
'Min ',    # 0x09
'Zui ',    # 0x0a
'Peng ',    # 0x0b
'An ',    # 0x0c
'Pi ',    # 0x0d
'Xian ',    # 0x0e
'Ya ',    # 0x0f
'Zhui ',    # 0x10
'Lei ',    # 0x11
'A ',    # 0x12
'Kong ',    # 0x13
'Ta ',    # 0x14
'Kun ',    # 0x15
'Du ',    # 0x16
'Wei ',    # 0x17
'Chui ',    # 0x18
'Zi ',    # 0x19
'Zheng ',    # 0x1a
'Ben ',    # 0x1b
'Nie ',    # 0x1c
'Cong ',    # 0x1d
'Qun ',    # 0x1e
'Tan ',    # 0x1f
'Ding ',    # 0x20
'Qi ',    # 0x21
'Qian ',    # 0x22
'Zhuo ',    # 0x23
'Qi ',    # 0x24
'Yu ',    # 0x25
'Jin ',    # 0x26
'Guan ',    # 0x27
'Mao ',    # 0x28
'Chang ',    # 0x29
'Tian ',    # 0x2a
'Xi ',    # 0x2b
'Lian ',    # 0x2c
'Tao ',    # 0x2d
'Gu ',    # 0x2e
'Cuo ',    # 0x2f
'Shu ',    # 0x30
'Zhen ',    # 0x31
'Lu ',    # 0x32
'Meng ',    # 0x33
'Lu ',    # 0x34
'Hua ',    # 0x35
'Biao ',    # 0x36
'Ga ',    # 0x37
'Lai ',    # 0x38
'Ken ',    # 0x39
'Kazari ',    # 0x3a
'Bu ',    # 0x3b
'Nai ',    # 0x3c
'Wan ',    # 0x3d
'Zan ',    # 0x3e
None,    # 0x3f
'De ',    # 0x40
'Xian ',    # 0x41
None,    # 0x42
'Huo ',    # 0x43
'Liang ',    # 0x44
None,    # 0x45
'Men ',    # 0x46
'Kai ',    # 0x47
'Ying ',    # 0x48
'Di ',    # 0x49
'Lian ',    # 0x4a
'Guo ',    # 0x4b
'Xian ',    # 0x4c
'Du ',    # 0x4d
'Tu ',    # 0x4e
'Wei ',    # 0x4f
'Cong ',    # 0x50
'Fu ',    # 0x51
'Rou ',    # 0x52
'Ji ',    # 0x53
'E ',    # 0x54
'Rou ',    # 0x55
'Chen ',    # 0x56
'Ti ',    # 0x57
'Zha ',    # 0x58
'Hong ',    # 0x59
'Yang ',    # 0x5a
'Duan ',    # 0x5b
'Xia ',    # 0x5c
'Yu ',    # 0x5d
'Keng ',    # 0x5e
'Xing ',    # 0x5f
'Huang ',    # 0x60
'Wei ',    # 0x61
'Fu ',    # 0x62
'Zhao ',    # 0x63
'Cha ',    # 0x64
'Qie ',    # 0x65
'She ',    # 0x66
'Hong ',    # 0x67
'Kui ',    # 0x68
'Tian ',    # 0x69
'Mou ',    # 0x6a
'Qiao ',    # 0x6b
'Qiao ',    # 0x6c
'Hou ',    # 0x6d
'Tou ',    # 0x6e
'Cong ',    # 0x6f
'Huan ',    # 0x70
'Ye ',    # 0x71
'Min ',    # 0x72
'Jian ',    # 0x73
'Duan ',    # 0x74
'Jian ',    # 0x75
'Song ',    # 0x76
'Kui ',    # 0x77
'Hu ',    # 0x78
'Xuan ',    # 0x79
'Duo ',    # 0x7a
'Jie ',    # 0x7b
'Zhen ',    # 0x7c
'Bian ',    # 0x7d
'Zhong ',    # 0x7e
'Zi ',    # 0x7f
'Xiu ',    # 0x80
'Ye ',    # 0x81
'Mei ',    # 0x82
'Pai ',    # 0x83
'Ai ',    # 0x84
'Jie ',    # 0x85
None,    # 0x86
'Mei ',    # 0x87
'Chuo ',    # 0x88
'Ta ',    # 0x89
'Bang ',    # 0x8a
'Xia ',    # 0x8b
'Lian ',    # 0x8c
'Suo ',    # 0x8d
'Xi ',    # 0x8e
'Liu ',    # 0x8f
'Zu ',    # 0x90
'Ye ',    # 0x91
'Nou ',    # 0x92
'Weng ',    # 0x93
'Rong ',    # 0x94
'Tang ',    # 0x95
'Suo ',    # 0x96
'Qiang ',    # 0x97
'Ge ',    # 0x98
'Shuo ',    # 0x99
'Chui ',    # 0x9a
'Bo ',    # 0x9b
'Pan ',    # 0x9c
'Sa ',    # 0x9d
'Bi ',    # 0x9e
'Sang ',    # 0x9f
'Gang ',    # 0xa0
'Zi ',    # 0xa1
'Wu ',    # 0xa2
'Ying ',    # 0xa3
'Huang ',    # 0xa4
'Tiao ',    # 0xa5
'Liu ',    # 0xa6
'Kai ',    # 0xa7
'Sun ',    # 0xa8
'Sha ',    # 0xa9
'Sou ',    # 0xaa
'Wan ',    # 0xab
'Hao ',    # 0xac
'Zhen ',    # 0xad
'Zhen ',    # 0xae
'Luo ',    # 0xaf
'Yi ',    # 0xb0
'Yuan ',    # 0xb1
'Tang ',    # 0xb2
'Nie ',    # 0xb3
'Xi ',    # 0xb4
'Jia ',    # 0xb5
'Ge ',    # 0xb6
'Ma ',    # 0xb7
'Juan ',    # 0xb8
'Kasugai ',    # 0xb9
'Habaki ',    # 0xba
'Suo ',    # 0xbb
None,    # 0xbc
None,    # 0xbd
None,    # 0xbe
'Na ',    # 0xbf
'Lu ',    # 0xc0
'Suo ',    # 0xc1
'Ou ',    # 0xc2
'Zu ',    # 0xc3
'Tuan ',    # 0xc4
'Xiu ',    # 0xc5
'Guan ',    # 0xc6
'Xuan ',    # 0xc7
'Lian ',    # 0xc8
'Shou ',    # 0xc9
'Ao ',    # 0xca
'Man ',    # 0xcb
'Mo ',    # 0xcc
'Luo ',    # 0xcd
'Bi ',    # 0xce
'Wei ',    # 0xcf
'Liu ',    # 0xd0
'Di ',    # 0xd1
'Qiao ',    # 0xd2
'Cong ',    # 0xd3
'Yi ',    # 0xd4
'Lu ',    # 0xd5
'Ao ',    # 0xd6
'Keng ',    # 0xd7
'Qiang ',    # 0xd8
'Cui ',    # 0xd9
'Qi ',    # 0xda
'Chang ',    # 0xdb
'Tang ',    # 0xdc
'Man ',    # 0xdd
'Yong ',    # 0xde
'Chan ',    # 0xdf
'Feng ',    # 0xe0
'Jing ',    # 0xe1
'Biao ',    # 0xe2
'Shu ',    # 0xe3
'Lou ',    # 0xe4
'Xiu ',    # 0xe5
'Cong ',    # 0xe6
'Long ',    # 0xe7
'Zan ',    # 0xe8
'Jian ',    # 0xe9
'Cao ',    # 0xea
'Li ',    # 0xeb
'Xia ',    # 0xec
'Xi ',    # 0xed
'Kang ',    # 0xee
None,    # 0xef
'Beng ',    # 0xf0
None,    # 0xf1
None,    # 0xf2
'Zheng ',    # 0xf3
'Lu ',    # 0xf4
'Hua ',    # 0xf5
'Ji ',    # 0xf6
'Pu ',    # 0xf7
'Hui ',    # 0xf8
'Qiang ',    # 0xf9
'Po ',    # 0xfa
'Lin ',    # 0xfb
'Suo ',    # 0xfc
'Xiu ',    # 0xfd
'San ',    # 0xfe
'Cheng ',    # 0xff
)
