data = (
'geul',    # 0x00
'geulg',    # 0x01
'geulm',    # 0x02
'geulb',    # 0x03
'geuls',    # 0x04
'geult',    # 0x05
'geulp',    # 0x06
'geulh',    # 0x07
'geum',    # 0x08
'geub',    # 0x09
'geubs',    # 0x0a
'geus',    # 0x0b
'geuss',    # 0x0c
'geung',    # 0x0d
'geuj',    # 0x0e
'geuc',    # 0x0f
'geuk',    # 0x10
'geut',    # 0x11
'geup',    # 0x12
'geuh',    # 0x13
'gyi',    # 0x14
'gyig',    # 0x15
'gyigg',    # 0x16
'gyigs',    # 0x17
'gyin',    # 0x18
'gyinj',    # 0x19
'gyinh',    # 0x1a
'gyid',    # 0x1b
'gyil',    # 0x1c
'gyilg',    # 0x1d
'gyilm',    # 0x1e
'gyilb',    # 0x1f
'gyils',    # 0x20
'gyilt',    # 0x21
'gyilp',    # 0x22
'gyilh',    # 0x23
'gyim',    # 0x24
'gyib',    # 0x25
'gyibs',    # 0x26
'gyis',    # 0x27
'gyiss',    # 0x28
'gying',    # 0x29
'gyij',    # 0x2a
'gyic',    # 0x2b
'gyik',    # 0x2c
'gyit',    # 0x2d
'gyip',    # 0x2e
'gyih',    # 0x2f
'gi',    # 0x30
'gig',    # 0x31
'gigg',    # 0x32
'gigs',    # 0x33
'gin',    # 0x34
'ginj',    # 0x35
'ginh',    # 0x36
'gid',    # 0x37
'gil',    # 0x38
'gilg',    # 0x39
'gilm',    # 0x3a
'gilb',    # 0x3b
'gils',    # 0x3c
'gilt',    # 0x3d
'gilp',    # 0x3e
'gilh',    # 0x3f
'gim',    # 0x40
'gib',    # 0x41
'gibs',    # 0x42
'gis',    # 0x43
'giss',    # 0x44
'ging',    # 0x45
'gij',    # 0x46
'gic',    # 0x47
'gik',    # 0x48
'git',    # 0x49
'gip',    # 0x4a
'gih',    # 0x4b
'gga',    # 0x4c
'ggag',    # 0x4d
'ggagg',    # 0x4e
'ggags',    # 0x4f
'ggan',    # 0x50
'gganj',    # 0x51
'gganh',    # 0x52
'ggad',    # 0x53
'ggal',    # 0x54
'ggalg',    # 0x55
'ggalm',    # 0x56
'ggalb',    # 0x57
'ggals',    # 0x58
'ggalt',    # 0x59
'ggalp',    # 0x5a
'ggalh',    # 0x5b
'ggam',    # 0x5c
'ggab',    # 0x5d
'ggabs',    # 0x5e
'ggas',    # 0x5f
'ggass',    # 0x60
'ggang',    # 0x61
'ggaj',    # 0x62
'ggac',    # 0x63
'ggak',    # 0x64
'ggat',    # 0x65
'ggap',    # 0x66
'ggah',    # 0x67
'ggae',    # 0x68
'ggaeg',    # 0x69
'ggaegg',    # 0x6a
'ggaegs',    # 0x6b
'ggaen',    # 0x6c
'ggaenj',    # 0x6d
'ggaenh',    # 0x6e
'ggaed',    # 0x6f
'ggael',    # 0x70
'ggaelg',    # 0x71
'ggaelm',    # 0x72
'ggaelb',    # 0x73
'ggaels',    # 0x74
'ggaelt',    # 0x75
'ggaelp',    # 0x76
'ggaelh',    # 0x77
'ggaem',    # 0x78
'ggaeb',    # 0x79
'ggaebs',    # 0x7a
'ggaes',    # 0x7b
'ggaess',    # 0x7c
'ggaeng',    # 0x7d
'ggaej',    # 0x7e
'ggaec',    # 0x7f
'ggaek',    # 0x80
'ggaet',    # 0x81
'ggaep',    # 0x82
'ggaeh',    # 0x83
'ggya',    # 0x84
'ggyag',    # 0x85
'ggyagg',    # 0x86
'ggyags',    # 0x87
'ggyan',    # 0x88
'ggyanj',    # 0x89
'ggyanh',    # 0x8a
'ggyad',    # 0x8b
'ggyal',    # 0x8c
'ggyalg',    # 0x8d
'ggyalm',    # 0x8e
'ggyalb',    # 0x8f
'ggyals',    # 0x90
'ggyalt',    # 0x91
'ggyalp',    # 0x92
'ggyalh',    # 0x93
'ggyam',    # 0x94
'ggyab',    # 0x95
'ggyabs',    # 0x96
'ggyas',    # 0x97
'ggyass',    # 0x98
'ggyang',    # 0x99
'ggyaj',    # 0x9a
'ggyac',    # 0x9b
'ggyak',    # 0x9c
'ggyat',    # 0x9d
'ggyap',    # 0x9e
'ggyah',    # 0x9f
'ggyae',    # 0xa0
'ggyaeg',    # 0xa1
'ggyaegg',    # 0xa2
'ggyaegs',    # 0xa3
'ggyaen',    # 0xa4
'ggyaenj',    # 0xa5
'ggyaenh',    # 0xa6
'ggyaed',    # 0xa7
'ggyael',    # 0xa8
'ggyaelg',    # 0xa9
'ggyaelm',    # 0xaa
'ggyaelb',    # 0xab
'ggyaels',    # 0xac
'ggyaelt',    # 0xad
'ggyaelp',    # 0xae
'ggyaelh',    # 0xaf
'ggyaem',    # 0xb0
'ggyaeb',    # 0xb1
'ggyaebs',    # 0xb2
'ggyaes',    # 0xb3
'ggyaess',    # 0xb4
'ggyaeng',    # 0xb5
'ggyaej',    # 0xb6
'ggyaec',    # 0xb7
'ggyaek',    # 0xb8
'ggyaet',    # 0xb9
'ggyaep',    # 0xba
'ggyaeh',    # 0xbb
'ggeo',    # 0xbc
'ggeog',    # 0xbd
'ggeogg',    # 0xbe
'ggeogs',    # 0xbf
'ggeon',    # 0xc0
'ggeonj',    # 0xc1
'ggeonh',    # 0xc2
'ggeod',    # 0xc3
'ggeol',    # 0xc4
'ggeolg',    # 0xc5
'ggeolm',    # 0xc6
'ggeolb',    # 0xc7
'ggeols',    # 0xc8
'ggeolt',    # 0xc9
'ggeolp',    # 0xca
'ggeolh',    # 0xcb
'ggeom',    # 0xcc
'ggeob',    # 0xcd
'ggeobs',    # 0xce
'ggeos',    # 0xcf
'ggeoss',    # 0xd0
'ggeong',    # 0xd1
'ggeoj',    # 0xd2
'ggeoc',    # 0xd3
'ggeok',    # 0xd4
'ggeot',    # 0xd5
'ggeop',    # 0xd6
'ggeoh',    # 0xd7
'gge',    # 0xd8
'ggeg',    # 0xd9
'ggegg',    # 0xda
'ggegs',    # 0xdb
'ggen',    # 0xdc
'ggenj',    # 0xdd
'ggenh',    # 0xde
'gged',    # 0xdf
'ggel',    # 0xe0
'ggelg',    # 0xe1
'ggelm',    # 0xe2
'ggelb',    # 0xe3
'ggels',    # 0xe4
'ggelt',    # 0xe5
'ggelp',    # 0xe6
'ggelh',    # 0xe7
'ggem',    # 0xe8
'ggeb',    # 0xe9
'ggebs',    # 0xea
'gges',    # 0xeb
'ggess',    # 0xec
'ggeng',    # 0xed
'ggej',    # 0xee
'ggec',    # 0xef
'ggek',    # 0xf0
'gget',    # 0xf1
'ggep',    # 0xf2
'ggeh',    # 0xf3
'ggyeo',    # 0xf4
'ggyeog',    # 0xf5
'ggyeogg',    # 0xf6
'ggyeogs',    # 0xf7
'ggyeon',    # 0xf8
'ggyeonj',    # 0xf9
'ggyeonh',    # 0xfa
'ggyeod',    # 0xfb
'ggyeol',    # 0xfc
'ggyeolg',    # 0xfd
'ggyeolm',    # 0xfe
'ggyeolb',    # 0xff
)
