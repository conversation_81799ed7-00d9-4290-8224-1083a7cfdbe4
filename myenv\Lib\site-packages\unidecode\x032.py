data = (
'(g)',    # 0x00
'(n)',    # 0x01
'(d)',    # 0x02
'(r)',    # 0x03
'(m)',    # 0x04
'(b)',    # 0x05
'(s)',    # 0x06
'()',    # 0x07
'(j)',    # 0x08
'(c)',    # 0x09
'(k)',    # 0x0a
'(t)',    # 0x0b
'(p)',    # 0x0c
'(h)',    # 0x0d
'(ga)',    # 0x0e
'(na)',    # 0x0f
'(da)',    # 0x10
'(ra)',    # 0x11
'(ma)',    # 0x12
'(ba)',    # 0x13
'(sa)',    # 0x14
'(a)',    # 0x15
'(ja)',    # 0x16
'(ca)',    # 0x17
'(ka)',    # 0x18
'(ta)',    # 0x19
'(pa)',    # 0x1a
'(ha)',    # 0x1b
'(ju)',    # 0x1c
None,    # 0x1d
None,    # 0x1e
None,    # 0x1f
'(1) ',    # 0x20
'(2) ',    # 0x21
'(3) ',    # 0x22
'(4) ',    # 0x23
'(5) ',    # 0x24
'(6) ',    # 0x25
'(7) ',    # 0x26
'(8) ',    # 0x27
'(9) ',    # 0x28
'(10) ',    # 0x29
'(Yue) ',    # 0x2a
'(Huo) ',    # 0x2b
'(Shui) ',    # 0x2c
'(Mu) ',    # 0x2d
'(Jin) ',    # 0x2e
'(Tu) ',    # 0x2f
'(Ri) ',    # 0x30
'(Zhu) ',    # 0x31
'(You) ',    # 0x32
'(She) ',    # 0x33
'(Ming) ',    # 0x34
'(Te) ',    # 0x35
'(Cai) ',    # 0x36
'(Zhu) ',    # 0x37
'(Lao) ',    # 0x38
'(Dai) ',    # 0x39
'(Hu) ',    # 0x3a
'(Xue) ',    # 0x3b
'(Jian) ',    # 0x3c
'(Qi) ',    # 0x3d
'(Zi) ',    # 0x3e
'(Xie) ',    # 0x3f
'(Ji) ',    # 0x40
'(Xiu) ',    # 0x41
'<<',    # 0x42
'>>',    # 0x43
None,    # 0x44
None,    # 0x45
None,    # 0x46
None,    # 0x47
None,    # 0x48
None,    # 0x49
None,    # 0x4a
None,    # 0x4b
None,    # 0x4c
None,    # 0x4d
None,    # 0x4e
None,    # 0x4f
None,    # 0x50
'21',    # 0x51
'22',    # 0x52
'23',    # 0x53
'24',    # 0x54
'25',    # 0x55
'26',    # 0x56
'27',    # 0x57
'28',    # 0x58
'29',    # 0x59
'30',    # 0x5a
'31',    # 0x5b
'32',    # 0x5c
'33',    # 0x5d
'34',    # 0x5e
'35',    # 0x5f
'(g)',    # 0x60
'(n)',    # 0x61
'(d)',    # 0x62
'(r)',    # 0x63
'(m)',    # 0x64
'(b)',    # 0x65
'(s)',    # 0x66
'()',    # 0x67
'(j)',    # 0x68
'(c)',    # 0x69
'(k)',    # 0x6a
'(t)',    # 0x6b
'(p)',    # 0x6c
'(h)',    # 0x6d
'(ga)',    # 0x6e
'(na)',    # 0x6f
'(da)',    # 0x70
'(ra)',    # 0x71
'(ma)',    # 0x72
'(ba)',    # 0x73
'(sa)',    # 0x74
'(a)',    # 0x75
'(ja)',    # 0x76
'(ca)',    # 0x77
'(ka)',    # 0x78
'(ta)',    # 0x79
'(pa)',    # 0x7a
'(ha)',    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
'KIS ',    # 0x7f
'(1) ',    # 0x80
'(2) ',    # 0x81
'(3) ',    # 0x82
'(4) ',    # 0x83
'(5) ',    # 0x84
'(6) ',    # 0x85
'(7) ',    # 0x86
'(8) ',    # 0x87
'(9) ',    # 0x88
'(10) ',    # 0x89
'(Yue) ',    # 0x8a
'(Huo) ',    # 0x8b
'(Shui) ',    # 0x8c
'(Mu) ',    # 0x8d
'(Jin) ',    # 0x8e
'(Tu) ',    # 0x8f
'(Ri) ',    # 0x90
'(Zhu) ',    # 0x91
'(You) ',    # 0x92
'(She) ',    # 0x93
'(Ming) ',    # 0x94
'(Te) ',    # 0x95
'(Cai) ',    # 0x96
'(Zhu) ',    # 0x97
'(Lao) ',    # 0x98
'(Mi) ',    # 0x99
'(Nan) ',    # 0x9a
'(Nu) ',    # 0x9b
'(Shi) ',    # 0x9c
'(You) ',    # 0x9d
'(Yin) ',    # 0x9e
'(Zhu) ',    # 0x9f
'(Xiang) ',    # 0xa0
'(Xiu) ',    # 0xa1
'(Xie) ',    # 0xa2
'(Zheng) ',    # 0xa3
'(Shang) ',    # 0xa4
'(Zhong) ',    # 0xa5
'(Xia) ',    # 0xa6
'(Zuo) ',    # 0xa7
'(You) ',    # 0xa8
'(Yi) ',    # 0xa9
'(Zong) ',    # 0xaa
'(Xue) ',    # 0xab
'(Jian) ',    # 0xac
'(Qi) ',    # 0xad
'(Zi) ',    # 0xae
'(Xie) ',    # 0xaf
'(Ye) ',    # 0xb0
'36',    # 0xb1
'37',    # 0xb2
'38',    # 0xb3
'39',    # 0xb4
'40',    # 0xb5
'41',    # 0xb6
'42',    # 0xb7
'43',    # 0xb8
'44',    # 0xb9
'45',    # 0xba
'46',    # 0xbb
'47',    # 0xbc
'48',    # 0xbd
'49',    # 0xbe
'50',    # 0xbf
'1M',    # 0xc0
'2M',    # 0xc1
'3M',    # 0xc2
'4M',    # 0xc3
'5M',    # 0xc4
'6M',    # 0xc5
'7M',    # 0xc6
'8M',    # 0xc7
'9M',    # 0xc8
'10M',    # 0xc9
'11M',    # 0xca
'12M',    # 0xcb
'Hg',    # 0xcc
'erg',    # 0xcd
'eV',    # 0xce
'LTD',    # 0xcf
'a',    # 0xd0
'i',    # 0xd1
'u',    # 0xd2
'u',    # 0xd3
'o',    # 0xd4
'ka',    # 0xd5
'ki',    # 0xd6
'ku',    # 0xd7
'ke',    # 0xd8
'ko',    # 0xd9
'sa',    # 0xda
'si',    # 0xdb
'su',    # 0xdc
'se',    # 0xdd
'so',    # 0xde
'ta',    # 0xdf
'ti',    # 0xe0
'tu',    # 0xe1
'te',    # 0xe2
'to',    # 0xe3
'na',    # 0xe4
'ni',    # 0xe5
'nu',    # 0xe6
'ne',    # 0xe7
'no',    # 0xe8
'ha',    # 0xe9
'hi',    # 0xea
'hu',    # 0xeb
'he',    # 0xec
'ho',    # 0xed
'ma',    # 0xee
'mi',    # 0xef
'mu',    # 0xf0
'me',    # 0xf1
'mo',    # 0xf2
'ya',    # 0xf3
'yu',    # 0xf4
'yo',    # 0xf5
'ra',    # 0xf6
'ri',    # 0xf7
'ru',    # 0xf8
're',    # 0xf9
'ro',    # 0xfa
'wa',    # 0xfb
'wi',    # 0xfc
'we',    # 0xfd
'wo',    # 0xfe
)
