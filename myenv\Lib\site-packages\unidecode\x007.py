data = (
'//',    # 0x00
'/',    # 0x01
',',    # 0x02
'!',    # 0x03
'!',    # 0x04
'-',    # 0x05
',',    # 0x06
',',    # 0x07
';',    # 0x08
'?',    # 0x09
'~',    # 0x0a
'{',    # 0x0b
'}',    # 0x0c
'*',    # 0x0d
None,    # 0x0e
'',    # 0x0f
'\'',    # 0x10
'',    # 0x11
'b',    # 0x12
'g',    # 0x13
'g',    # 0x14
'd',    # 0x15
'd',    # 0x16
'h',    # 0x17
'w',    # 0x18
'z',    # 0x19
'H',    # 0x1a
't',    # 0x1b
't',    # 0x1c
'y',    # 0x1d
'yh',    # 0x1e
'k',    # 0x1f
'l',    # 0x20
'm',    # 0x21
'n',    # 0x22
's',    # 0x23
's',    # 0x24
'`',    # 0x25
'p',    # 0x26
'p',    # 0x27
'S',    # 0x28
'q',    # 0x29
'r',    # 0x2a
'sh',    # 0x2b
't',    # 0x2c
None,    # 0x2d
None,    # 0x2e
None,    # 0x2f
'a',    # 0x30
'a',    # 0x31
'a',    # 0x32
'A',    # 0x33
'A',    # 0x34
'A',    # 0x35
'e',    # 0x36
'e',    # 0x37
'e',    # 0x38
'E',    # 0x39
'i',    # 0x3a
'i',    # 0x3b
'u',    # 0x3c
'u',    # 0x3d
'u',    # 0x3e
'o',    # 0x3f
'',    # 0x40
'`',    # 0x41
'\'',    # 0x42
'',    # 0x43
'',    # 0x44
'X',    # 0x45
'Q',    # 0x46
'@',    # 0x47
'@',    # 0x48
'|',    # 0x49
'+',    # 0x4a
None,    # 0x4b
None,    # 0x4c
None,    # 0x4d
None,    # 0x4e
None,    # 0x4f
None,    # 0x50
None,    # 0x51
None,    # 0x52
None,    # 0x53
None,    # 0x54
None,    # 0x55
None,    # 0x56
None,    # 0x57
None,    # 0x58
None,    # 0x59
None,    # 0x5a
None,    # 0x5b
None,    # 0x5c
None,    # 0x5d
None,    # 0x5e
None,    # 0x5f
None,    # 0x60
None,    # 0x61
None,    # 0x62
None,    # 0x63
None,    # 0x64
None,    # 0x65
None,    # 0x66
None,    # 0x67
None,    # 0x68
None,    # 0x69
None,    # 0x6a
None,    # 0x6b
None,    # 0x6c
None,    # 0x6d
None,    # 0x6e
None,    # 0x6f
None,    # 0x70
None,    # 0x71
None,    # 0x72
None,    # 0x73
None,    # 0x74
None,    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
'h',    # 0x80
'sh',    # 0x81
'n',    # 0x82
'r',    # 0x83
'b',    # 0x84
'L',    # 0x85
'k',    # 0x86
'\'',    # 0x87
'v',    # 0x88
'm',    # 0x89
'f',    # 0x8a
'dh',    # 0x8b
'th',    # 0x8c
'l',    # 0x8d
'g',    # 0x8e
'ny',    # 0x8f
's',    # 0x90
'd',    # 0x91
'z',    # 0x92
't',    # 0x93
'y',    # 0x94
'p',    # 0x95
'j',    # 0x96
'ch',    # 0x97
'tt',    # 0x98
'hh',    # 0x99
'kh',    # 0x9a
'th',    # 0x9b
'z',    # 0x9c
'sh',    # 0x9d
's',    # 0x9e
'd',    # 0x9f
't',    # 0xa0
'z',    # 0xa1
'`',    # 0xa2
'gh',    # 0xa3
'q',    # 0xa4
'w',    # 0xa5
'a',    # 0xa6
'aa',    # 0xa7
'i',    # 0xa8
'ee',    # 0xa9
'u',    # 0xaa
'oo',    # 0xab
'e',    # 0xac
'ey',    # 0xad
'o',    # 0xae
'oa',    # 0xaf
'',    # 0xb0
None,    # 0xb1
None,    # 0xb2
None,    # 0xb3
None,    # 0xb4
None,    # 0xb5
None,    # 0xb6
None,    # 0xb7
None,    # 0xb8
None,    # 0xb9
None,    # 0xba
None,    # 0xbb
None,    # 0xbc
None,    # 0xbd
None,    # 0xbe
None,    # 0xbf
None,    # 0xc0
None,    # 0xc1
None,    # 0xc2
None,    # 0xc3
None,    # 0xc4
None,    # 0xc5
None,    # 0xc6
None,    # 0xc7
None,    # 0xc8
None,    # 0xc9
None,    # 0xca
None,    # 0xcb
None,    # 0xcc
None,    # 0xcd
None,    # 0xce
None,    # 0xcf
None,    # 0xd0
None,    # 0xd1
None,    # 0xd2
None,    # 0xd3
None,    # 0xd4
None,    # 0xd5
None,    # 0xd6
None,    # 0xd7
None,    # 0xd8
None,    # 0xd9
None,    # 0xda
None,    # 0xdb
None,    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
None,    # 0xe6
None,    # 0xe7
None,    # 0xe8
None,    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
