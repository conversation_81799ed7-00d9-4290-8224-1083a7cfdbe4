data = (
'',    # 0x00
'',    # 0x01
'',    # 0x02
'',    # 0x03
'',    # 0x04
'',    # 0x05
'',    # 0x06
'',    # 0x07
'',    # 0x08
'',    # 0x09
'',    # 0x0a
'',    # 0x0b
'',    # 0x0c
'',    # 0x0d
'',    # 0x0e
'',    # 0x0f
'',    # 0x10
'',    # 0x11
'',    # 0x12
'',    # 0x13
None,    # 0x14
None,    # 0x15
None,    # 0x16
None,    # 0x17
None,    # 0x18
'',    # 0x19
'',    # 0x1a
'',    # 0x1b
'',    # 0x1c
'',    # 0x1d
'',    # 0x1e
'',    # 0x1f
'',    # 0x20
'',    # 0x21
'',    # 0x22
'',    # 0x23
'',    # 0x24
'',    # 0x25
'',    # 0x26
'',    # 0x27
'',    # 0x28
'',    # 0x29
'',    # 0x2a
'',    # 0x2b
'',    # 0x2c
'',    # 0x2d
'',    # 0x2e
'',    # 0x2f
'',    # 0x30
'',    # 0x31
'',    # 0x32
'',    # 0x33
'',    # 0x34
'',    # 0x35
'',    # 0x36
'',    # 0x37
'',    # 0x38
'',    # 0x39
'',    # 0x3a
'',    # 0x3b
'',    # 0x3c
'',    # 0x3d
'',    # 0x3e
'',    # 0x3f
'',    # 0x40
'',    # 0x41
'',    # 0x42
'',    # 0x43
'',    # 0x44
'',    # 0x45
'',    # 0x46
'',    # 0x47
'',    # 0x48
'',    # 0x49
'',    # 0x4a
'',    # 0x4b
'',    # 0x4c
'',    # 0x4d
'',    # 0x4e
'',    # 0x4f
'',    # 0x50
'',    # 0x51
'',    # 0x52
'',    # 0x53
'',    # 0x54
'',    # 0x55
'',    # 0x56
'',    # 0x57
'',    # 0x58
'',    # 0x59
'',    # 0x5a
'',    # 0x5b
'',    # 0x5c
'',    # 0x5d
'',    # 0x5e
'',    # 0x5f
'',    # 0x60
'',    # 0x61
'',    # 0x62
'',    # 0x63
'',    # 0x64
'',    # 0x65
'',    # 0x66
'',    # 0x67
'',    # 0x68
'',    # 0x69
'',    # 0x6a
'',    # 0x6b
'',    # 0x6c
'',    # 0x6d
'',    # 0x6e
'#',    # 0x6f
'',    # 0x70
'',    # 0x71
None,    # 0x72
None,    # 0x73
None,    # 0x74
None,    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
None,    # 0x80
None,    # 0x81
None,    # 0x82
None,    # 0x83
None,    # 0x84
None,    # 0x85
None,    # 0x86
None,    # 0x87
None,    # 0x88
None,    # 0x89
None,    # 0x8a
None,    # 0x8b
None,    # 0x8c
None,    # 0x8d
None,    # 0x8e
None,    # 0x8f
None,    # 0x90
None,    # 0x91
None,    # 0x92
None,    # 0x93
None,    # 0x94
None,    # 0x95
None,    # 0x96
None,    # 0x97
None,    # 0x98
None,    # 0x99
None,    # 0x9a
None,    # 0x9b
None,    # 0x9c
None,    # 0x9d
None,    # 0x9e
None,    # 0x9f
None,    # 0xa0
None,    # 0xa1
None,    # 0xa2
None,    # 0xa3
None,    # 0xa4
None,    # 0xa5
None,    # 0xa6
None,    # 0xa7
None,    # 0xa8
None,    # 0xa9
None,    # 0xaa
None,    # 0xab
None,    # 0xac
None,    # 0xad
None,    # 0xae
None,    # 0xaf
None,    # 0xb0
None,    # 0xb1
None,    # 0xb2
None,    # 0xb3
None,    # 0xb4
None,    # 0xb5
None,    # 0xb6
None,    # 0xb7
None,    # 0xb8
None,    # 0xb9
None,    # 0xba
None,    # 0xbb
None,    # 0xbc
None,    # 0xbd
None,    # 0xbe
None,    # 0xbf
None,    # 0xc0
None,    # 0xc1
None,    # 0xc2
None,    # 0xc3
None,    # 0xc4
None,    # 0xc5
None,    # 0xc6
None,    # 0xc7
None,    # 0xc8
None,    # 0xc9
None,    # 0xca
None,    # 0xcb
None,    # 0xcc
None,    # 0xcd
None,    # 0xce
None,    # 0xcf
None,    # 0xd0
None,    # 0xd1
None,    # 0xd2
None,    # 0xd3
None,    # 0xd4
None,    # 0xd5
None,    # 0xd6
None,    # 0xd7
None,    # 0xd8
None,    # 0xd9
None,    # 0xda
None,    # 0xdb
None,    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
None,    # 0xe6
None,    # 0xe7
None,    # 0xe8
None,    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
