data = (
' @ ',    # 0x00
' ... ',    # 0x01
', ',    # 0x02
'. ',    # 0x03
': ',    # 0x04
' // ',    # 0x05
'',    # 0x06
'-',    # 0x07
', ',    # 0x08
'. ',    # 0x09
'',    # 0x0a
'',    # 0x0b
'',    # 0x0c
'',    # 0x0d
'',    # 0x0e
None,    # 0x0f
'0',    # 0x10
'1',    # 0x11
'2',    # 0x12
'3',    # 0x13
'4',    # 0x14
'5',    # 0x15
'6',    # 0x16
'7',    # 0x17
'8',    # 0x18
'9',    # 0x19
None,    # 0x1a
None,    # 0x1b
None,    # 0x1c
None,    # 0x1d
None,    # 0x1e
None,    # 0x1f
'a',    # 0x20
'e',    # 0x21
'i',    # 0x22
'o',    # 0x23
'u',    # 0x24
'O',    # 0x25
'U',    # 0x26
'ee',    # 0x27
'n',    # 0x28
'ng',    # 0x29
'b',    # 0x2a
'p',    # 0x2b
'q',    # 0x2c
'g',    # 0x2d
'm',    # 0x2e
'l',    # 0x2f
's',    # 0x30
'sh',    # 0x31
't',    # 0x32
'd',    # 0x33
'ch',    # 0x34
'j',    # 0x35
'y',    # 0x36
'r',    # 0x37
'w',    # 0x38
'f',    # 0x39
'k',    # 0x3a
'kha',    # 0x3b
'ts',    # 0x3c
'z',    # 0x3d
'h',    # 0x3e
'zr',    # 0x3f
'lh',    # 0x40
'zh',    # 0x41
'ch',    # 0x42
'-',    # 0x43
'e',    # 0x44
'i',    # 0x45
'o',    # 0x46
'u',    # 0x47
'O',    # 0x48
'U',    # 0x49
'ng',    # 0x4a
'b',    # 0x4b
'p',    # 0x4c
'q',    # 0x4d
'g',    # 0x4e
'm',    # 0x4f
't',    # 0x50
'd',    # 0x51
'ch',    # 0x52
'j',    # 0x53
'ts',    # 0x54
'y',    # 0x55
'w',    # 0x56
'k',    # 0x57
'g',    # 0x58
'h',    # 0x59
'jy',    # 0x5a
'ny',    # 0x5b
'dz',    # 0x5c
'e',    # 0x5d
'i',    # 0x5e
'iy',    # 0x5f
'U',    # 0x60
'u',    # 0x61
'ng',    # 0x62
'k',    # 0x63
'g',    # 0x64
'h',    # 0x65
'p',    # 0x66
'sh',    # 0x67
't',    # 0x68
'd',    # 0x69
'j',    # 0x6a
'f',    # 0x6b
'g',    # 0x6c
'h',    # 0x6d
'ts',    # 0x6e
'z',    # 0x6f
'r',    # 0x70
'ch',    # 0x71
'zh',    # 0x72
'i',    # 0x73
'k',    # 0x74
'r',    # 0x75
'f',    # 0x76
'zh',    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
None,    # 0x80
'H',    # 0x81
'X',    # 0x82
'W',    # 0x83
'M',    # 0x84
' 3 ',    # 0x85
' 333 ',    # 0x86
'a',    # 0x87
'i',    # 0x88
'k',    # 0x89
'ng',    # 0x8a
'c',    # 0x8b
'tt',    # 0x8c
'tth',    # 0x8d
'dd',    # 0x8e
'nn',    # 0x8f
't',    # 0x90
'd',    # 0x91
'p',    # 0x92
'ph',    # 0x93
'ss',    # 0x94
'zh',    # 0x95
'z',    # 0x96
'a',    # 0x97
't',    # 0x98
'zh',    # 0x99
'gh',    # 0x9a
'ng',    # 0x9b
'c',    # 0x9c
'jh',    # 0x9d
'tta',    # 0x9e
'ddh',    # 0x9f
't',    # 0xa0
'dh',    # 0xa1
'ss',    # 0xa2
'cy',    # 0xa3
'zh',    # 0xa4
'z',    # 0xa5
'u',    # 0xa6
'y',    # 0xa7
'bh',    # 0xa8
'\'',    # 0xa9
None,    # 0xaa
None,    # 0xab
None,    # 0xac
None,    # 0xad
None,    # 0xae
None,    # 0xaf
None,    # 0xb0
None,    # 0xb1
None,    # 0xb2
None,    # 0xb3
None,    # 0xb4
None,    # 0xb5
None,    # 0xb6
None,    # 0xb7
None,    # 0xb8
None,    # 0xb9
None,    # 0xba
None,    # 0xbb
None,    # 0xbc
None,    # 0xbd
None,    # 0xbe
None,    # 0xbf
None,    # 0xc0
None,    # 0xc1
None,    # 0xc2
None,    # 0xc3
None,    # 0xc4
None,    # 0xc5
None,    # 0xc6
None,    # 0xc7
None,    # 0xc8
None,    # 0xc9
None,    # 0xca
None,    # 0xcb
None,    # 0xcc
None,    # 0xcd
None,    # 0xce
None,    # 0xcf
None,    # 0xd0
None,    # 0xd1
None,    # 0xd2
None,    # 0xd3
None,    # 0xd4
None,    # 0xd5
None,    # 0xd6
None,    # 0xd7
None,    # 0xd8
None,    # 0xd9
None,    # 0xda
None,    # 0xdb
None,    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
None,    # 0xe0
None,    # 0xe1
None,    # 0xe2
None,    # 0xe3
None,    # 0xe4
None,    # 0xe5
None,    # 0xe6
None,    # 0xe7
None,    # 0xe8
None,    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
