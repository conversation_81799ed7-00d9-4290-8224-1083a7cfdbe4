data = (
None,    # 0x00
None,    # 0x01
None,    # 0x02
None,    # 0x03
None,    # 0x04
None,    # 0x05
None,    # 0x06
None,    # 0x07
None,    # 0x08
None,    # 0x09
None,    # 0x0a
None,    # 0x0b
None,    # 0x0c
None,    # 0x0d
None,    # 0x0e
None,    # 0x0f
None,    # 0x10
None,    # 0x11
None,    # 0x12
None,    # 0x13
None,    # 0x14
None,    # 0x15
None,    # 0x16
None,    # 0x17
None,    # 0x18
None,    # 0x19
None,    # 0x1a
None,    # 0x1b
None,    # 0x1c
None,    # 0x1d
None,    # 0x1e
None,    # 0x1f
None,    # 0x20
None,    # 0x21
None,    # 0x22
None,    # 0x23
None,    # 0x24
None,    # 0x25
None,    # 0x26
None,    # 0x27
None,    # 0x28
None,    # 0x29
None,    # 0x2a
None,    # 0x2b
None,    # 0x2c
None,    # 0x2d
None,    # 0x2e
None,    # 0x2f
None,    # 0x30
None,    # 0x31
None,    # 0x32
None,    # 0x33
None,    # 0x34
None,    # 0x35
None,    # 0x36
None,    # 0x37
None,    # 0x38
None,    # 0x39
None,    # 0x3a
None,    # 0x3b
None,    # 0x3c
None,    # 0x3d
None,    # 0x3e
None,    # 0x3f
None,    # 0x40
None,    # 0x41
None,    # 0x42
None,    # 0x43
None,    # 0x44
None,    # 0x45
None,    # 0x46
None,    # 0x47
None,    # 0x48
None,    # 0x49
None,    # 0x4a
None,    # 0x4b
None,    # 0x4c
None,    # 0x4d
None,    # 0x4e
None,    # 0x4f
None,    # 0x50
None,    # 0x51
None,    # 0x52
None,    # 0x53
None,    # 0x54
None,    # 0x55
None,    # 0x56
None,    # 0x57
None,    # 0x58
None,    # 0x59
None,    # 0x5a
None,    # 0x5b
None,    # 0x5c
None,    # 0x5d
None,    # 0x5e
None,    # 0x5f
None,    # 0x60
None,    # 0x61
None,    # 0x62
None,    # 0x63
None,    # 0x64
None,    # 0x65
None,    # 0x66
None,    # 0x67
None,    # 0x68
None,    # 0x69
None,    # 0x6a
None,    # 0x6b
None,    # 0x6c
None,    # 0x6d
None,    # 0x6e
None,    # 0x6f
None,    # 0x70
None,    # 0x71
None,    # 0x72
None,    # 0x73
None,    # 0x74
None,    # 0x75
None,    # 0x76
None,    # 0x77
None,    # 0x78
None,    # 0x79
None,    # 0x7a
None,    # 0x7b
None,    # 0x7c
None,    # 0x7d
None,    # 0x7e
None,    # 0x7f
'k',    # 0x80
'kh',    # 0x81
'g',    # 0x82
'gh',    # 0x83
'ng',    # 0x84
'c',    # 0x85
'ch',    # 0x86
'j',    # 0x87
'jh',    # 0x88
'ny',    # 0x89
't',    # 0x8a
'tth',    # 0x8b
'd',    # 0x8c
'ddh',    # 0x8d
'nn',    # 0x8e
't',    # 0x8f
'th',    # 0x90
'd',    # 0x91
'dh',    # 0x92
'n',    # 0x93
'p',    # 0x94
'ph',    # 0x95
'b',    # 0x96
'bh',    # 0x97
'm',    # 0x98
'y',    # 0x99
'r',    # 0x9a
'l',    # 0x9b
'v',    # 0x9c
'sh',    # 0x9d
'ss',    # 0x9e
's',    # 0x9f
'h',    # 0xa0
'l',    # 0xa1
'q',    # 0xa2
'a',    # 0xa3
'aa',    # 0xa4
'i',    # 0xa5
'ii',    # 0xa6
'u',    # 0xa7
'uk',    # 0xa8
'uu',    # 0xa9
'uuv',    # 0xaa
'ry',    # 0xab
'ryy',    # 0xac
'ly',    # 0xad
'lyy',    # 0xae
'e',    # 0xaf
'ai',    # 0xb0
'oo',    # 0xb1
'oo',    # 0xb2
'au',    # 0xb3
'a',    # 0xb4
'aa',    # 0xb5
'aa',    # 0xb6
'i',    # 0xb7
'ii',    # 0xb8
'y',    # 0xb9
'yy',    # 0xba
'u',    # 0xbb
'uu',    # 0xbc
'ua',    # 0xbd
'oe',    # 0xbe
'ya',    # 0xbf
'ie',    # 0xc0
'e',    # 0xc1
'ae',    # 0xc2
'ai',    # 0xc3
'oo',    # 0xc4
'au',    # 0xc5
'M',    # 0xc6
'H',    # 0xc7
'a`',    # 0xc8
'',    # 0xc9
'',    # 0xca
'',    # 0xcb
'r',    # 0xcc
'',    # 0xcd
'!',    # 0xce
'',    # 0xcf
'',    # 0xd0
'',    # 0xd1
'',    # 0xd2
'',    # 0xd3
'.',    # 0xd4
' // ',    # 0xd5
':',    # 0xd6
'+',    # 0xd7
'++',    # 0xd8
' * ',    # 0xd9
' /// ',    # 0xda
'KR',    # 0xdb
'\'',    # 0xdc
None,    # 0xdd
None,    # 0xde
None,    # 0xdf
'0',    # 0xe0
'1',    # 0xe1
'2',    # 0xe2
'3',    # 0xe3
'4',    # 0xe4
'5',    # 0xe5
'6',    # 0xe6
'7',    # 0xe7
'8',    # 0xe8
'9',    # 0xe9
None,    # 0xea
None,    # 0xeb
None,    # 0xec
None,    # 0xed
None,    # 0xee
None,    # 0xef
None,    # 0xf0
None,    # 0xf1
None,    # 0xf2
None,    # 0xf3
None,    # 0xf4
None,    # 0xf5
None,    # 0xf6
None,    # 0xf7
None,    # 0xf8
None,    # 0xf9
None,    # 0xfa
None,    # 0xfb
None,    # 0xfc
None,    # 0xfd
None,    # 0xfe
)
