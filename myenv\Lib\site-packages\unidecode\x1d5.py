data = (
'w',    # 0x00
'x',    # 0x01
'y',    # 0x02
'z',    # 0x03
'A',    # 0x04
'B',    # 0x05
None,    # 0x06
'D',    # 0x07
'E',    # 0x08
'F',    # 0x09
'G',    # 0x0a
None,    # 0x0b
None,    # 0x0c
'J',    # 0x0d
'K',    # 0x0e
'L',    # 0x0f
'M',    # 0x10
'N',    # 0x11
'O',    # 0x12
'P',    # 0x13
'Q',    # 0x14
None,    # 0x15
'S',    # 0x16
'T',    # 0x17
'U',    # 0x18
'V',    # 0x19
'W',    # 0x1a
'X',    # 0x1b
'Y',    # 0x1c
None,    # 0x1d
'a',    # 0x1e
'b',    # 0x1f
'c',    # 0x20
'd',    # 0x21
'e',    # 0x22
'f',    # 0x23
'g',    # 0x24
'h',    # 0x25
'i',    # 0x26
'j',    # 0x27
'k',    # 0x28
'l',    # 0x29
'm',    # 0x2a
'n',    # 0x2b
'o',    # 0x2c
'p',    # 0x2d
'q',    # 0x2e
'r',    # 0x2f
's',    # 0x30
't',    # 0x31
'u',    # 0x32
'v',    # 0x33
'w',    # 0x34
'x',    # 0x35
'y',    # 0x36
'z',    # 0x37
'A',    # 0x38
'B',    # 0x39
None,    # 0x3a
'D',    # 0x3b
'E',    # 0x3c
'F',    # 0x3d
'G',    # 0x3e
None,    # 0x3f
'I',    # 0x40
'J',    # 0x41
'K',    # 0x42
'L',    # 0x43
'M',    # 0x44
None,    # 0x45
'O',    # 0x46
None,    # 0x47
None,    # 0x48
None,    # 0x49
'S',    # 0x4a
'T',    # 0x4b
'U',    # 0x4c
'V',    # 0x4d
'W',    # 0x4e
'X',    # 0x4f
'Y',    # 0x50
None,    # 0x51
'a',    # 0x52
'b',    # 0x53
'c',    # 0x54
'd',    # 0x55
'e',    # 0x56
'f',    # 0x57
'g',    # 0x58
'h',    # 0x59
'i',    # 0x5a
'j',    # 0x5b
'k',    # 0x5c
'l',    # 0x5d
'm',    # 0x5e
'n',    # 0x5f
'o',    # 0x60
'p',    # 0x61
'q',    # 0x62
'r',    # 0x63
's',    # 0x64
't',    # 0x65
'u',    # 0x66
'v',    # 0x67
'w',    # 0x68
'x',    # 0x69
'y',    # 0x6a
'z',    # 0x6b
'A',    # 0x6c
'B',    # 0x6d
'C',    # 0x6e
'D',    # 0x6f
'E',    # 0x70
'F',    # 0x71
'G',    # 0x72
'H',    # 0x73
'I',    # 0x74
'J',    # 0x75
'K',    # 0x76
'L',    # 0x77
'M',    # 0x78
'N',    # 0x79
'O',    # 0x7a
'P',    # 0x7b
'Q',    # 0x7c
'R',    # 0x7d
'S',    # 0x7e
'T',    # 0x7f
'U',    # 0x80
'V',    # 0x81
'W',    # 0x82
'X',    # 0x83
'Y',    # 0x84
'Z',    # 0x85
'a',    # 0x86
'b',    # 0x87
'c',    # 0x88
'd',    # 0x89
'e',    # 0x8a
'f',    # 0x8b
'g',    # 0x8c
'h',    # 0x8d
'i',    # 0x8e
'j',    # 0x8f
'k',    # 0x90
'l',    # 0x91
'm',    # 0x92
'n',    # 0x93
'o',    # 0x94
'p',    # 0x95
'q',    # 0x96
'r',    # 0x97
's',    # 0x98
't',    # 0x99
'u',    # 0x9a
'v',    # 0x9b
'w',    # 0x9c
'x',    # 0x9d
'y',    # 0x9e
'z',    # 0x9f
'A',    # 0xa0
'B',    # 0xa1
'C',    # 0xa2
'D',    # 0xa3
'E',    # 0xa4
'F',    # 0xa5
'G',    # 0xa6
'H',    # 0xa7
'I',    # 0xa8
'J',    # 0xa9
'K',    # 0xaa
'L',    # 0xab
'M',    # 0xac
'N',    # 0xad
'O',    # 0xae
'P',    # 0xaf
'Q',    # 0xb0
'R',    # 0xb1
'S',    # 0xb2
'T',    # 0xb3
'U',    # 0xb4
'V',    # 0xb5
'W',    # 0xb6
'X',    # 0xb7
'Y',    # 0xb8
'Z',    # 0xb9
'a',    # 0xba
'b',    # 0xbb
'c',    # 0xbc
'd',    # 0xbd
'e',    # 0xbe
'f',    # 0xbf
'g',    # 0xc0
'h',    # 0xc1
'i',    # 0xc2
'j',    # 0xc3
'k',    # 0xc4
'l',    # 0xc5
'm',    # 0xc6
'n',    # 0xc7
'o',    # 0xc8
'p',    # 0xc9
'q',    # 0xca
'r',    # 0xcb
's',    # 0xcc
't',    # 0xcd
'u',    # 0xce
'v',    # 0xcf
'w',    # 0xd0
'x',    # 0xd1
'y',    # 0xd2
'z',    # 0xd3
'A',    # 0xd4
'B',    # 0xd5
'C',    # 0xd6
'D',    # 0xd7
'E',    # 0xd8
'F',    # 0xd9
'G',    # 0xda
'H',    # 0xdb
'I',    # 0xdc
'J',    # 0xdd
'K',    # 0xde
'L',    # 0xdf
'M',    # 0xe0
'N',    # 0xe1
'O',    # 0xe2
'P',    # 0xe3
'Q',    # 0xe4
'R',    # 0xe5
'S',    # 0xe6
'T',    # 0xe7
'U',    # 0xe8
'V',    # 0xe9
'W',    # 0xea
'X',    # 0xeb
'Y',    # 0xec
'Z',    # 0xed
'a',    # 0xee
'b',    # 0xef
'c',    # 0xf0
'd',    # 0xf1
'e',    # 0xf2
'f',    # 0xf3
'g',    # 0xf4
'h',    # 0xf5
'i',    # 0xf6
'j',    # 0xf7
'k',    # 0xf8
'l',    # 0xf9
'm',    # 0xfa
'n',    # 0xfb
'o',    # 0xfc
'p',    # 0xfd
'q',    # 0xfe
'r',    # 0xff
)
