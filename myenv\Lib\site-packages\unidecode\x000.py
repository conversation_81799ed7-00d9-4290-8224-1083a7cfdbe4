data = (
# Code points u+007f and below are equivalent to ASCII and are handled by a
# special case in the code. Hence they are not present in this table.
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
'', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',

'',    # 0x80
'',    # 0x81
'',    # 0x82
'',    # 0x83
'',    # 0x84
'',    # 0x85
'',    # 0x86
'',    # 0x87
'',    # 0x88
'',    # 0x89
'',    # 0x8a
'',    # 0x8b
'',    # 0x8c
'',    # 0x8d
'',    # 0x8e
'',    # 0x8f
'',    # 0x90
'',    # 0x91
'',    # 0x92
'',    # 0x93
'',    # 0x94
'',    # 0x95
'',    # 0x96
'',    # 0x97
'',    # 0x98
'',    # 0x99
'',    # 0x9a
'',    # 0x9b
'',    # 0x9c
'',    # 0x9d
'',    # 0x9e
'',    # 0x9f
' ',    # 0xa0
'!',    # 0xa1
'C/',    # 0xa2

# Not "GBP" - Pound Sign is used for more than just British Pounds.
'PS',    # 0xa3

'$?',    # 0xa4
'Y=',    # 0xa5
'|',    # 0xa6
'SS',    # 0xa7
'"',    # 0xa8
'(c)',    # 0xa9
'a',    # 0xaa
'<<',    # 0xab
'!',    # 0xac
'',    # 0xad
'(r)',    # 0xae
'-',    # 0xaf
'deg',    # 0xb0
'+-',    # 0xb1

# These might be combined with other superscript digits (u+2070 - u+2079)
'2',    # 0xb2
'3',    # 0xb3

'\'',    # 0xb4
'u',    # 0xb5
'P',    # 0xb6
'*',    # 0xb7
',',    # 0xb8
'1',    # 0xb9
'o',    # 0xba
'>>',    # 0xbb
' 1/4',    # 0xbc
' 1/2',    # 0xbd
' 3/4',    # 0xbe
'?',    # 0xbf
'A',    # 0xc0
'A',    # 0xc1
'A',    # 0xc2
'A',    # 0xc3

# Not "AE" - used in languages other than German
'A',    # 0xc4

'A',    # 0xc5
'AE',    # 0xc6
'C',    # 0xc7
'E',    # 0xc8
'E',    # 0xc9
'E',    # 0xca
'E',    # 0xcb
'I',    # 0xcc
'I',    # 0xcd
'I',    # 0xce
'I',    # 0xcf
'D',    # 0xd0
'N',    # 0xd1
'O',    # 0xd2
'O',    # 0xd3
'O',    # 0xd4
'O',    # 0xd5

# Not "OE" - used in languages other than German
'O',    # 0xd6

'x',    # 0xd7
'O',    # 0xd8
'U',    # 0xd9
'U',    # 0xda
'U',    # 0xdb

# Not "UE" - used in languages other than German
'U',    # 0xdc

'Y',    # 0xdd
'Th',    # 0xde
'ss',    # 0xdf
'a',    # 0xe0
'a',    # 0xe1
'a',    # 0xe2
'a',    # 0xe3

# Not "ae" - used in languages other than German
'a',    # 0xe4

'a',    # 0xe5
'ae',    # 0xe6
'c',    # 0xe7
'e',    # 0xe8
'e',    # 0xe9
'e',    # 0xea
'e',    # 0xeb
'i',    # 0xec
'i',    # 0xed
'i',    # 0xee
'i',    # 0xef
'd',    # 0xf0
'n',    # 0xf1
'o',    # 0xf2
'o',    # 0xf3
'o',    # 0xf4
'o',    # 0xf5

# Not "oe" - used in languages other than German
'o',    # 0xf6

'/',    # 0xf7
'o',    # 0xf8
'u',    # 0xf9
'u',    # 0xfa
'u',    # 0xfb

# Not "ue" - used in languages other than German
'u',    # 0xfc

'y',    # 0xfd
'th',    # 0xfe
'y',    # 0xff
)
